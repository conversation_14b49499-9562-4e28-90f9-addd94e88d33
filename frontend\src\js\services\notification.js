// Notification Service for Warehouse Management System
export class NotificationService {
    constructor() {
        this.container = null;
        this.notifications = new Map();
        this.defaultDuration = 5000;
        this.maxNotifications = 5;
        this.notificationId = 0;
        
        // Initialize container
        this.initContainer();
    }

    // Initialize notification container
    initContainer() {
        this.container = document.getElementById('notifications');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'notifications';
            this.container.className = 'fixed top-4 right-4 z-50 space-y-2';
            document.body.appendChild(this.container);
        }
    }

    // Initialize service
    async init() {
        try {
            // Request notification permission if supported
            if ('Notification' in window && Notification.permission === 'default') {
                await Notification.requestPermission();
            }
            
            console.log('Notification service initialized');
            return true;
        } catch (error) {
            console.error('Failed to initialize notification service:', error);
            return false;
        }
    }

    // Show notification
    show(message, type = 'info', options = {}) {
        const {
            title = null,
            duration = this.defaultDuration,
            persistent = false,
            actions = [],
            onClick = null,
            onClose = null
        } = options;

        // Generate unique ID
        const id = ++this.notificationId;

        // Create notification element
        const notification = this.createNotificationElement(id, message, type, title, actions);

        // Add to container
        this.container.appendChild(notification);
        this.notifications.set(id, {
            element: notification,
            type,
            message,
            title,
            timestamp: Date.now(),
            onClick,
            onClose
        });

        // Animate in
        requestAnimationFrame(() => {
            notification.classList.remove('translate-x-full', 'opacity-0');
            notification.classList.add('translate-x-0', 'opacity-100');
        });

        // Auto-remove if not persistent
        if (!persistent && duration > 0) {
            setTimeout(() => {
                this.remove(id);
            }, duration);
        }

        // Limit number of notifications
        this.limitNotifications();

        // Show browser notification if permission granted and app is not visible
        if (document.hidden && 'Notification' in window && Notification.permission === 'granted') {
            this.showBrowserNotification(title || this.getTypeTitle(type), message, type);
        }

        return id;
    }

    // Create notification element
    createNotificationElement(id, message, type, title, actions) {
        const notification = document.createElement('div');
        notification.className = `notification transform transition-all duration-300 translate-x-full opacity-0 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden`;
        notification.dataset.id = id;

        const iconClass = this.getIconClass(type);
        const colorClass = this.getColorClass(type);

        notification.innerHTML = `
            <div class="p-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="${colorClass} rounded-full p-1">
                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                ${iconClass}
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3 w-0 flex-1 pt-0.5">
                        ${title ? `<p class="text-sm font-medium text-gray-900">${title}</p>` : ''}
                        <p class="text-sm text-gray-500 ${title ? 'mt-1' : ''}">${message}</p>
                        ${actions.length > 0 ? this.createActionsHTML(actions, id) : ''}
                    </div>
                    <div class="ml-4 flex-shrink-0 flex">
                        <button class="notification-close bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" data-id="${id}">
                            <span class="sr-only">Close</span>
                            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add event listeners
        this.addNotificationEventListeners(notification, id);

        return notification;
    }

    // Create actions HTML
    createActionsHTML(actions, notificationId) {
        if (actions.length === 0) return '';

        const actionsHTML = actions.map(action => `
            <button class="notification-action text-sm font-medium text-indigo-600 hover:text-indigo-500 mr-3" 
                    data-id="${notificationId}" 
                    data-action="${action.id}">
                ${action.label}
            </button>
        `).join('');

        return `<div class="mt-3">${actionsHTML}</div>`;
    }

    // Add event listeners to notification
    addNotificationEventListeners(notification, id) {
        // Close button
        const closeBtn = notification.querySelector('.notification-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.remove(id);
            });
        }

        // Action buttons
        const actionBtns = notification.querySelectorAll('.notification-action');
        actionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const actionId = btn.dataset.action;
                this.handleAction(id, actionId);
            });
        });

        // Click handler
        notification.addEventListener('click', () => {
            const notificationData = this.notifications.get(id);
            if (notificationData && notificationData.onClick) {
                notificationData.onClick(id);
            }
        });
    }

    // Handle notification action
    handleAction(notificationId, actionId) {
        const notificationData = this.notifications.get(notificationId);
        if (notificationData && notificationData.onAction) {
            notificationData.onAction(actionId, notificationId);
        }
        
        // Remove notification after action
        this.remove(notificationId);
    }

    // Remove notification
    remove(id) {
        const notificationData = this.notifications.get(id);
        if (!notificationData) return;

        const { element, onClose } = notificationData;

        // Animate out
        element.classList.remove('translate-x-0', 'opacity-100');
        element.classList.add('translate-x-full', 'opacity-0');

        // Remove from DOM after animation
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
            this.notifications.delete(id);

            // Call onClose callback
            if (onClose) {
                onClose(id);
            }
        }, 300);
    }

    // Show browser notification
    showBrowserNotification(title, message, type) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const options = {
                body: message,
                icon: this.getNotificationIcon(type),
                badge: '/icons/badge-72x72.png',
                tag: 'warehouse-notification',
                requireInteraction: false,
                silent: false
            };

            const notification = new Notification(title, options);

            notification.onclick = () => {
                window.focus();
                notification.close();
            };

            // Auto-close after 5 seconds
            setTimeout(() => {
                notification.close();
            }, 5000);
        }
    }

    // Limit number of notifications
    limitNotifications() {
        const notificationElements = this.container.children;
        
        while (notificationElements.length > this.maxNotifications) {
            const oldestElement = notificationElements[0];
            const oldestId = parseInt(oldestElement.dataset.id);
            this.remove(oldestId);
        }
    }

    // Get icon class for notification type
    getIconClass(type) {
        const icons = {
            success: '<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />',
            error: '<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />',
            warning: '<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />',
            info: '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />'
        };
        
        return icons[type] || icons.info;
    }

    // Get color class for notification type
    getColorClass(type) {
        const colors = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };
        
        return colors[type] || colors.info;
    }

    // Get title for notification type
    getTypeTitle(type) {
        const titles = {
            success: 'Успешно',
            error: 'Ошибка',
            warning: 'Предупреждение',
            info: 'Информация'
        };
        
        return titles[type] || titles.info;
    }

    // Get notification icon for browser notifications
    getNotificationIcon(type) {
        const icons = {
            success: '/icons/notification-success.png',
            error: '/icons/notification-error.png',
            warning: '/icons/notification-warning.png',
            info: '/icons/notification-info.png'
        };
        
        return icons[type] || '/icons/icon-192x192.png';
    }

    // Convenience methods
    success(message, options = {}) {
        return this.show(message, 'success', options);
    }

    error(message, options = {}) {
        return this.show(message, 'error', { ...options, duration: 0 }); // Persistent by default
    }

    warning(message, options = {}) {
        return this.show(message, 'warning', options);
    }

    info(message, options = {}) {
        return this.show(message, 'info', options);
    }

    // Clear all notifications
    clearAll() {
        const notificationIds = Array.from(this.notifications.keys());
        notificationIds.forEach(id => this.remove(id));
    }

    // Get notification count
    getCount() {
        return this.notifications.size;
    }

    // Check if notifications are supported
    isSupported() {
        return 'Notification' in window;
    }

    // Get notification permission status
    getPermissionStatus() {
        return 'Notification' in window ? Notification.permission : 'unsupported';
    }

    // Request notification permission
    async requestPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            return await Notification.requestPermission();
        }
        return Notification.permission;
    }
}

export default NotificationService;
