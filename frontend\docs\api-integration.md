# API Integration Guide - Warehouse Management PWA

## Обзор

Данное руководство описывает интеграцию frontend PWA приложения с backend API системы управления складом.

## Архитектура API

### Базовая информация
- **Протокол**: HTTP/HTTPS
- **Формат данных**: JSON
- **Аутентификация**: JWT (JSON Web Tokens)
- **Версия API**: 2.0.0
- **Base URL**: 
  - Development: `http://localhost/warehouse/api`
  - Production: `/api` (относительный путь)

### Структура ответов
Все API ответы следуют единому формату:

```json
{
  "success": true|false,
  "data": {}, // Данные ответа
  "error": null|{
    "message": "Описание ошибки",
    "code": "ERROR_CODE",
    "details": {}
  },
  "_meta": {
    "timestamp": "2025-06-03T12:30:45Z",
    "version": "2.0.0",
    "totalCount": 100,
    "pageCount": 5,
    "currentPage": 1,
    "perPage": 20
  }
}
```

## Конфигурация API

### Настройка клиента
```javascript
// src/config/api.js
export const API_CONFIG = {
    baseURL: 'http://localhost/warehouse/api',
    timeout: 10000,
    retries: 3,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    }
};
```

### Инициализация клиента
```javascript
import apiClient from './api/client.js';

// Использование
const response = await apiClient.get('/products');
```

## Аутентификация

### Логин
```javascript
// POST /api/auth/login
const loginResponse = await apiClient.post('/auth/login', {
    username: 'admin',
    password: 'password'
});

if (loginResponse.success) {
    const { token, user } = loginResponse.data;
    // Токен автоматически сохраняется в localStorage/sessionStorage
}
```

### Верификация токена
```javascript
// POST /api/auth/verify
const verifyResponse = await apiClient.post('/auth/verify', {
    token: 'your-jwt-token'
});
```

### Автоматическая аутентификация
API клиент автоматически:
- Добавляет JWT токен в заголовки запросов
- Обновляет токен при необходимости
- Перенаправляет на страницу входа при истечении токена

## Основные эндпоинты

### Товары (Products)

#### Получение списка товаров
```javascript
const products = await apiClient.get('/products', {
    params: {
        category: 1,
        search: 'товар',
        page: 1,
        limit: 20
    }
});
```

#### Создание товара
```javascript
const newProduct = await apiClient.post('/products', {
    name: 'Новый товар',
    barcode: '1234567890123',
    category_id: 1,
    unit_type: 'piece',
    price_per_unit: 25.99,
    current_stock: 100,
    description: 'Описание товара'
});
```

#### Обновление товара
```javascript
const updatedProduct = await apiClient.put('/products/123', {
    name: 'Обновленное название',
    price_per_unit: 29.99
});
```

#### Генерация штрих-кода
```javascript
const barcodeResult = await apiClient.post('/products/123/generate-barcode');
```

### Быстрые операции

#### Быстрая продажа по штрих-коду
```javascript
const saleResult = await apiClient.post('/sales/scan-barcode', {
    barcode: '1234567890123',
    quantity: 2,
    price_per_unit: 25.99,
    notes: 'Продажа через сканер'
});
```

#### Быстрое поступление
```javascript
const incomeResult = await apiClient.post('/income/scan-barcode', {
    barcode: '1234567890123',
    quantity: 10,
    price_per_unit: 20.00,
    notes: 'Поступление от поставщика'
});
```

#### Пакетная обработка
```javascript
const batchResult = await apiClient.post('/income/batch', {
    items: [
        {
            barcode: '1234567890123',
            quantity: 5,
            price_per_unit: 20.00
        },
        {
            barcode: '9876543210987',
            quantity: 3,
            price_per_unit: 15.50
        }
    ]
});
```

### Отчеты

#### Отчет по остаткам
```javascript
const stockReport = await apiClient.get('/reports/stock', {
    params: {
        category_id: 1,
        low_stock: true,
        limit: 100
    }
});
```

#### Отчет по продажам
```javascript
const salesReport = await apiClient.get('/reports/sales', {
    params: {
        start_date: '2025-06-01',
        end_date: '2025-06-30',
        group_by: 'day'
    }
});
```

## Обработка ошибок

### Типы ошибок
```javascript
try {
    const response = await apiClient.get('/products');
} catch (error) {
    switch (error.status) {
        case 401:
            // Неавторизован - перенаправить на логин
            break;
        case 403:
            // Недостаточно прав
            break;
        case 404:
            // Ресурс не найден
            break;
        case 422:
            // Ошибка валидации
            console.log(error.data.details);
            break;
        case 500:
            // Ошибка сервера
            break;
        default:
            // Другие ошибки
            break;
    }
}
```

### Обработка офлайн режима
```javascript
const response = await apiClient.get('/products');

if (response._meta?.offline) {
    // Данные получены из кэша (офлайн режим)
    showNotification('Данные из кэша', 'warning');
}

if (response._meta?.queued) {
    // Запрос добавлен в очередь для синхронизации
    showNotification('Операция будет выполнена при восстановлении соединения', 'info');
}
```

## Кэширование

### Автоматическое кэширование
API клиент автоматически кэширует GET запросы для:
- Списка товаров
- Категорий
- Отчетов
- Истории чеков

### Управление кэшем
```javascript
// Очистка кэша
apiClient.clearCache();

// Принудительное обновление без кэша
const freshData = await apiClient.get('/products', { 
    cache: false 
});
```

## Офлайн синхронизация

### Очередь запросов
Мутирующие операции (POST, PUT, DELETE) автоматически добавляются в очередь при отсутствии соединения:

```javascript
// Этот запрос будет выполнен при восстановлении соединения
const result = await apiClient.post('/sales', saleData);

if (result._meta?.queued) {
    console.log('Операция добавлена в очередь синхронизации');
}
```

### Мониторинг синхронизации
```javascript
import { SyncService } from './services/sync.js';

const syncService = new SyncService();

// Подписка на события синхронизации
syncService.on('sync:complete', (data) => {
    console.log(`Синхронизировано ${data.syncedCount} операций`);
});

syncService.on('sync:error', (error) => {
    console.error('Ошибка синхронизации:', error);
});
```

## Загрузка файлов

### Загрузка изображений товаров
```javascript
const fileInput = document.getElementById('product-image');
const file = fileInput.files[0];

const uploadResult = await apiClient.upload('/products/123/image', file, {
    description: 'Фото товара'
});
```

### Экспорт отчетов
```javascript
// Скачивание CSV отчета
await apiClient.download('/reports/stock/export?format=csv', 'stock-report.csv');

// Скачивание PDF чека
await apiClient.download('/receipts/123/pdf', 'receipt-123.pdf');
```

## Производительность

### Пагинация
```javascript
// Загрузка с пагинацией
const products = await apiClient.get('/products', {
    params: {
        page: 1,
        limit: 20
    }
});

console.log(`Страница ${products._meta.currentPage} из ${products._meta.pageCount}`);
console.log(`Всего записей: ${products._meta.totalCount}`);
```

### Оптимизация запросов
```javascript
// Дебаунсинг поиска
const debouncedSearch = debounce(async (query) => {
    const results = await apiClient.get('/products', {
        params: { search: query }
    });
}, 300);

// Использование
searchInput.addEventListener('input', (e) => {
    debouncedSearch(e.target.value);
});
```

## Безопасность

### CORS настройки
Backend должен быть настроен для поддержки CORS:

```php
// PHP backend example
header('Access-Control-Allow-Origin: https://your-frontend-domain.com');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
```

### Валидация на клиенте
```javascript
function validateProductData(data) {
    const errors = {};
    
    if (!data.name || data.name.trim().length < 2) {
        errors.name = 'Название должно содержать минимум 2 символа';
    }
    
    if (!data.price_per_unit || data.price_per_unit <= 0) {
        errors.price_per_unit = 'Цена должна быть больше 0';
    }
    
    return {
        isValid: Object.keys(errors).length === 0,
        errors
    };
}
```

## Тестирование API

### Модульные тесты
```javascript
// tests/api.test.js
import apiClient from '../src/js/api/client.js';

describe('API Client', () => {
    test('should authenticate user', async () => {
        const response = await apiClient.post('/auth/login', {
            username: 'test',
            password: 'test'
        });
        
        expect(response.success).toBe(true);
        expect(response.data.token).toBeDefined();
    });
});
```

### Мокирование API
```javascript
// Для тестирования без реального API
const mockApiClient = {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn()
};
```

## Мониторинг и отладка

### Логирование запросов
```javascript
// Включение debug режима
localStorage.setItem('api_debug', 'true');

// API клиент будет логировать все запросы
```

### Метрики производительности
```javascript
// Измерение времени ответа API
const startTime = performance.now();
const response = await apiClient.get('/products');
const endTime = performance.now();

console.log(`API request took ${endTime - startTime} milliseconds`);
```

## Лучшие практики

1. **Всегда обрабатывайте ошибки**
2. **Используйте типизацию данных**
3. **Кэшируйте статические данные**
4. **Реализуйте retry логику**
5. **Валидируйте данные на клиенте**
6. **Используйте дебаунсинг для поиска**
7. **Показывайте состояние загрузки**
8. **Обрабатывайте офлайн режим**

---

Для получения актуальной документации по API эндпоинтам обратитесь к backend документации или Swagger/OpenAPI спецификации.
