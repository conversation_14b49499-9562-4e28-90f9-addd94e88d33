<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Система управления складом - PWA приложение для мобильного управления товарами, продажами и отчетами">
    <meta name="theme-color" content="#1f2937">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Склад">
    
    <title>Система управления складом</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icons/favicon-16x16.png">
    <link rel="apple-touch-icon" href="icons/apple-touch-icon.png">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../src/css/main.css">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="../src/js/app.js" as="script">
    <link rel="preload" href="../src/js/api/client.js" as="script">
</head>
<body class="bg-gray-50 font-sans">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <h2 class="text-xl font-semibold text-gray-700">Загрузка...</h2>
            <p class="text-gray-500 mt-2">Инициализация приложения</p>
        </div>
    </div>

    <!-- App Container -->
    <div id="app" class="hidden min-h-screen">
        <!-- Navigation Header -->
        <header id="app-header" class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo and Title -->
                    <div class="flex items-center">
                        <button id="menu-toggle" class="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                        <div class="flex-shrink-0 flex items-center ml-2 md:ml-0">
                            <h1 class="text-xl font-bold text-gray-900">Склад</h1>
                        </div>
                    </div>

                    <!-- Desktop Navigation -->
                    <nav class="hidden md:flex space-x-8">
                        <a href="#dashboard" class="nav-link text-primary-600 border-primary-600">Панель</a>
                        <a href="#products" class="nav-link">Товары</a>
                        <a href="#sales" class="nav-link">Продажи</a>
                        <a href="#income" class="nav-link">Поступления</a>
                        <a href="#reports" class="nav-link">Отчеты</a>
                    </nav>

                    <!-- User Menu -->
                    <div class="flex items-center space-x-4">
                        <!-- Connection Status -->
                        <div id="connection-status" class="flex items-center">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600 hidden sm:inline">Онлайн</span>
                        </div>
                        
                        <!-- User Avatar -->
                        <div class="relative">
                            <button id="user-menu-button" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <div class="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                                    <span class="text-white font-medium" id="user-initials">U</span>
                                </div>
                            </button>
                            
                            <!-- User Dropdown -->
                            <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                <div class="px-4 py-2 text-sm text-gray-700 border-b">
                                    <div class="font-medium" id="user-name">Пользователь</div>
                                    <div class="text-gray-500" id="user-email"><EMAIL></div>
                                </div>
                                <a href="#profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Профиль</a>
                                <a href="#settings" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Настройки</a>
                                <button id="logout-btn" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Выйти</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Mobile Navigation -->
        <nav id="mobile-nav" class="md:hidden bg-white border-b border-gray-200 hidden">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="#dashboard" class="mobile-nav-link bg-primary-50 text-primary-700">Панель</a>
                <a href="#products" class="mobile-nav-link">Товары</a>
                <a href="#sales" class="mobile-nav-link">Продажи</a>
                <a href="#income" class="mobile-nav-link">Поступления</a>
                <a href="#reports" class="mobile-nav-link">Отчеты</a>
            </div>
        </nav>

        <!-- Main Content -->
        <main id="main-content" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <!-- Page content will be dynamically loaded here -->
        </main>

        <!-- Quick Action Button (FAB) -->
        <div class="fixed bottom-6 right-6 z-30">
            <div class="relative">
                <button id="fab-main" class="bg-primary-600 hover:bg-primary-700 text-white rounded-full p-4 shadow-lg transition-all duration-200 transform hover:scale-105">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                </button>
                
                <!-- FAB Menu -->
                <div id="fab-menu" class="absolute bottom-16 right-0 hidden space-y-2">
                    <button class="fab-option bg-green-600 hover:bg-green-700" data-action="quick-sale">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                        <span class="fab-tooltip">Быстрая продажа</span>
                    </button>
                    <button class="fab-option bg-blue-600 hover:bg-blue-700" data-action="quick-income">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                        </svg>
                        <span class="fab-tooltip">Быстрое поступление</span>
                    </button>
                    <button class="fab-option bg-purple-600 hover:bg-purple-700" data-action="scan-barcode">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                        </svg>
                        <span class="fab-tooltip">Сканировать</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Login Screen -->
    <div id="login-screen" class="hidden fixed inset-0 bg-gray-50 z-50">
        <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
            <div class="max-w-md w-full space-y-8">
                <div class="text-center">
                    <h2 class="mt-6 text-3xl font-extrabold text-gray-900">Вход в систему</h2>
                    <p class="mt-2 text-sm text-gray-600">Система управления складом</p>
                </div>
                <form id="login-form" class="mt-8 space-y-6">
                    <div class="space-y-4">
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700">Имя пользователя</label>
                            <input id="username" name="username" type="text" required 
                                   class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                                   placeholder="Введите имя пользователя">
                        </div>
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700">Пароль</label>
                            <input id="password" name="password" type="password" required 
                                   class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                                   placeholder="Введите пароль">
                        </div>
                    </div>

                    <div id="login-error" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
                        <span class="block sm:inline" id="login-error-message"></span>
                    </div>

                    <div>
                        <button type="submit" id="login-submit" 
                                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed">
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <svg class="h-5 w-5 text-primary-500 group-hover:text-primary-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                            Войти
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notifications" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Modals Container -->
    <div id="modals-container"></div>

    <!-- Scripts -->
    <script type="module" src="../src/js/app.js"></script>
</body>
</html>
