<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description"
        content="Система управления складом - PWA приложение для мобильного управления товарами, продажами и отчетами">
    <meta name="theme-color" content="#1f2937">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Склад">

    <title>Система управления складом</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Icons -->
    <link rel="icon" type="image/svg+xml" href="icons/icon.svg">
    <link rel="apple-touch-icon" href="icons/icon.svg">

    <!-- Inline CSS instead of Tailwind for reliability -->
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f9fafb;
        }

        .hidden {
            display: none !important;
        }

        /* Loading screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
            z-index: 50;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .loading-content {
            text-align: center;
        }

        .spinner {
            width: 48px;
            height: 48px;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #2563eb;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Login screen */
        .login-screen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #f9fafb;
            z-index: 50;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 48px 16px;
        }

        .login-form {
            max-width: 400px;
            width: 100%;
            background: white;
            padding: 32px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #2563eb;
        }

        .btn-primary {
            width: 100%;
            padding: 12px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        /* App layout */
        .app {
            min-height: 100vh;
        }

        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 16px;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 20px;
            font-weight: bold;
            color: #111827;
        }

        .nav {
            display: flex;
            gap: 32px;
        }

        .nav-link {
            color: #6b7280;
            text-decoration: none;
            padding: 8px 0;
            border-bottom: 2px solid transparent;
        }

        .nav-link.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
        }

        .nav-link:hover {
            color: #374151;
        }

        /* Main content */
        .main {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px 16px;
        }

        .dashboard {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 24px;
        }

        .dashboard-title {
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .stat-card {
            padding: 16px;
            border-radius: 8px;
        }

        .stat-card.blue {
            background: #eff6ff;
        }

        .stat-card.green {
            background: #f0fdf4;
        }

        .stat-card.yellow {
            background: #fefce8;
        }

        .stat-card.purple {
            background: #faf5ff;
        }

        .stat-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
        }

        .blue .stat-title {
            color: #1e3a8a;
        }

        .blue .stat-value {
            color: #2563eb;
        }

        .green .stat-title {
            color: #14532d;
        }

        .green .stat-value {
            color: #16a34a;
        }

        .yellow .stat-title {
            color: #713f12;
        }

        .yellow .stat-value {
            color: #ca8a04;
        }

        .purple .stat-title {
            color: #581c87;
        }

        .purple .stat-value {
            color: #9333ea;
        }

        /* Actions */
        .actions-title {
            font-size: 20px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 16px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .action-btn {
            padding: 16px;
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s;
        }

        .action-btn:hover {
            transform: translateY(-1px);
        }

        .action-btn.blue {
            background: #2563eb;
        }

        .action-btn.blue:hover {
            background: #1d4ed8;
        }

        .action-btn.green {
            background: #16a34a;
        }

        .action-btn.green:hover {
            background: #15803d;
        }

        .action-btn.purple {
            background: #9333ea;
        }

        .action-btn.purple:hover {
            background: #7c3aed;
        }

        /* FAB */
        .fab {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 56px;
            height: 56px;
            background: #2563eb;
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .fab:hover {
            background: #1d4ed8;
        }

        /* Error */
        .error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 16px;
        }
    </style>


</head>

<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="spinner"></div>
            <h2 style="font-size: 20px; font-weight: 600; color: #374151; margin-bottom: 8px;">Загрузка...</h2>
            <p style="color: #6b7280;">Инициализация приложения</p>
        </div>
    </div>

    <!-- App Container -->
    <div id="app" class="hidden app">
        <!-- Navigation Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">Склад</div>
                <nav class="nav">
                    <a href="#dashboard" class="nav-link active">Панель</a>
                    <a href="#products" class="nav-link">Товары</a>
                    <a href="#sales" class="nav-link">Продажи</a>
                    <a href="#income" class="nav-link">Поступления</a>
                    <a href="#reports" class="nav-link">Отчеты</a>
                </nav>
                <div style="display: flex; align-items: center; gap: 16px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 8px; height: 8px; background: #16a34a; border-radius: 50%;"></div>
                        <span style="font-size: 14px; color: #6b7280;">Онлайн</span>
                    </div>
                    <div
                        style="width: 32px; height: 32px; background: #2563eb; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 500;">
                        U</div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main id="main-content" class="main">
            <!-- Page content will be dynamically loaded here -->
        </main>

        <!-- Quick Action Button (FAB) -->
        <button id="fab-main" class="fab">+</button>
    </div>

    <!-- Login Screen -->
    <div id="login-screen" class="hidden login-screen">
        <div class="login-form">
            <div style="text-align: center; margin-bottom: 24px;">
                <h2 style="font-size: 28px; font-weight: bold; color: #111827; margin-bottom: 8px;">Вход в систему</h2>
                <p style="color: #6b7280;">Система управления складом</p>
            </div>
            <form id="login-form">
                <div class="form-group">
                    <label for="username" class="form-label">Имя пользователя</label>
                    <input id="username" name="username" type="text" required class="form-input"
                        placeholder="Введите имя пользователя">
                </div>
                <div class="form-group">
                    <label for="password" class="form-label">Пароль</label>
                    <input id="password" name="password" type="password" required class="form-input"
                        placeholder="Введите пароль">
                </div>
                <div id="login-error" class="hidden error">
                    <span id="login-error-message"></span>
                </div>
                <button type="submit" id="login-submit" class="btn-primary">Войти</button>
            </form>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notifications" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Modals Container -->
    <div id="modals-container"></div>

    <!-- Scripts -->
    <script>
        // Simple app initialization
        document.addEventListener('DOMContentLoaded', function () {
            console.log('Warehouse Management PWA loaded');

            // Hide loading screen
            const loadingScreen = document.getElementById('loading-screen');
            const app = document.getElementById('app');
            const loginScreen = document.getElementById('login-screen');

            setTimeout(() => {
                loadingScreen.classList.add('hidden');
                loginScreen.classList.remove('hidden');
            }, 1000);

            // Login form handler
            const loginForm = document.getElementById('login-form');
            loginForm.addEventListener('submit', function (e) {
                e.preventDefault();

                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                // Simple validation for demo
                if (username && password) {
                    loginScreen.classList.add('hidden');
                    app.classList.remove('hidden');

                    // Load dashboard content
                    const mainContent = document.getElementById('main-content');
                    mainContent.innerHTML = `
                        <div class="dashboard">
                            <h1 class="dashboard-title">Панель управления</h1>
                            <div class="stats-grid">
                                <div class="stat-card blue">
                                    <div class="stat-title">Товары</div>
                                    <div class="stat-value">1,234</div>
                                </div>
                                <div class="stat-card green">
                                    <div class="stat-title">Продажи</div>
                                    <div class="stat-value">₽45,678</div>
                                </div>
                                <div class="stat-card yellow">
                                    <div class="stat-title">Поступления</div>
                                    <div class="stat-value">567</div>
                                </div>
                                <div class="stat-card purple">
                                    <div class="stat-title">Остатки</div>
                                    <div class="stat-value">₽123,456</div>
                                </div>
                            </div>
                            <div>
                                <h2 class="actions-title">Быстрые действия</h2>
                                <div class="actions-grid">
                                    <button class="action-btn blue">
                                        ➕ Добавить товар
                                    </button>
                                    <button class="action-btn green">
                                        💰 Быстрая продажа
                                    </button>
                                    <button class="action-btn purple">
                                        📱 Сканировать штрих-код
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    const errorDiv = document.getElementById('login-error');
                    const errorMessage = document.getElementById('login-error-message');
                    errorMessage.textContent = 'Пожалуйста, введите имя пользователя и пароль';
                    errorDiv.classList.remove('hidden');
                }
            });

            // FAB button
            const fabMain = document.getElementById('fab-main');
            if (fabMain) {
                fabMain.addEventListener('click', function () {
                    alert('Быстрые действия: Добавить товар, Продажа, Сканирование');
                });
            }
        });

        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw-simple.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
</body>

</html>