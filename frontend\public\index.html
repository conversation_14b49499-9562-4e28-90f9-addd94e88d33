<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Система управления складом</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="Система управления складом - PWA приложение">
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Склад">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Icons -->
    <link rel="icon" type="image/svg+xml" href="icons/icon.svg">
    <link rel="apple-touch-icon" href="icons/icon.svg">

    <style>
        /* CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }

        /* Utility Classes */
        .hidden {
            display: none !important;
        }

        .flex {
            display: flex;
        }

        .grid {
            display: grid;
        }

        .relative {
            position: relative;
        }

        .fixed {
            position: fixed;
        }

        .absolute {
            position: absolute;
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Login Screen */
        .login-screen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .login-form {
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            margin: 20px;
        }

        .login-title {
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 8px;
            color: #1e293b;
        }

        .login-subtitle {
            text-align: center;
            color: #64748b;
            margin-bottom: 32px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .btn-primary {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        /* App Layout */
        .app {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Header */
        .header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 64px;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo::before {
            content: "📦";
            font-size: 28px;
        }

        /* Navigation */
        .nav {
            display: flex;
            gap: 32px;
        }

        .nav-link {
            color: #64748b;
            text-decoration: none;
            padding: 8px 0;
            border-bottom: 2px solid transparent;
            font-weight: 500;
            transition: all 0.2s;
            cursor: pointer;
        }

        .nav-link:hover {
            color: #1e293b;
        }

        .nav-link.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }

        /* Mobile Navigation */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #64748b;
        }

        .mobile-nav {
            display: none;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 20px;
        }

        .mobile-nav.active {
            display: block;
        }

        .mobile-nav-link {
            display: block;
            color: #64748b;
            text-decoration: none;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
            font-weight: 500;
            cursor: pointer;
        }

        .mobile-nav-link:hover,
        .mobile-nav-link.active {
            color: #3b82f6;
        }

        /* User Info */
        .user-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #64748b;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            cursor: pointer;
        }

        /* Main Content */
        .main {
            flex: 1;
            max-width: 1200px;
            margin: 0 auto;
            padding: 32px 20px;
            width: 100%;
        }

        /* Page Sections */
        .page-section {
            display: none;
        }

        .page-section.active {
            display: block;
        }

        /* Dashboard */
        .dashboard-header {
            margin-bottom: 32px;
        }

        .dashboard-title {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .dashboard-subtitle {
            color: #64748b;
            font-size: 16px;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            border-left: 4px solid;
            transition: transform 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-card.blue {
            border-left-color: #3b82f6;
        }

        .stat-card.green {
            border-left-color: #10b981;
        }

        .stat-card.yellow {
            border-left-color: #f59e0b;
        }

        .stat-card.purple {
            border-left-color: #8b5cf6;
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .stat-title {
            font-size: 14px;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-icon {
            font-size: 24px;
        }

        .stat-value {
            font-size: 36px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .stat-change {
            font-size: 14px;
            font-weight: 500;
        }

        .stat-change.positive {
            color: #10b981;
        }

        .stat-change.negative {
            color: #ef4444;
        }

        /* Quick Actions */
        .quick-actions {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 20px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .action-btn {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            color: #1e293b;
        }

        .action-btn:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .action-icon {
            font-size: 32px;
            margin-bottom: 12px;
            display: block;
        }

        .action-title {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .action-desc {
            font-size: 14px;
            color: #64748b;
        }

        /* FAB */
        .fab {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
            transition: all 0.2s;
            z-index: 1000;
        }

        .fab:hover {
            transform: scale(1.1);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .header-content {
                padding: 0 16px;
            }

            .main {
                padding: 20px 16px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }

            .connection-status span {
                display: none;
            }
        }
    </style>
</head>

<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="spinner"></div>
            <h2 style="font-size: 24px; margin-bottom: 8px;">Загрузка...</h2>
            <p>Инициализация системы управления складом</p>
        </div>
    </div>

    <!-- Login Screen -->
    <div id="login-screen" class="login-screen hidden">
        <div class="login-form">
            <h2 class="login-title">Вход в систему</h2>
            <p class="login-subtitle">Система управления складом</p>

            <form id="login-form">
                <div class="form-group">
                    <label for="username" class="form-label">Имя пользователя</label>
                    <input type="text" id="username" name="username" class="form-input"
                        placeholder="Введите имя пользователя" required>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Пароль</label>
                    <input type="password" id="password" name="password" class="form-input" placeholder="Введите пароль"
                        required>
                </div>

                <div id="login-error" class="error-message hidden">
                    <span id="login-error-message"></span>
                </div>

                <button type="submit" class="btn-primary">Войти</button>
            </form>
        </div>
    </div>

    <!-- Main App -->
    <div id="app" class="app hidden">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">Склад</div>

                <!-- Desktop Navigation -->
                <nav class="nav">
                    <a class="nav-link active" data-page="dashboard">Панель</a>
                    <a class="nav-link" data-page="products">Товары</a>
                    <a class="nav-link" data-page="sales">Продажи</a>
                    <a class="nav-link" data-page="income">Поступления</a>
                    <a class="nav-link" data-page="reports">Отчеты</a>
                </nav>

                <!-- Mobile Menu Button -->
                <button class="mobile-menu-btn" id="mobile-menu-btn">☰</button>

                <!-- User Info -->
                <div class="user-info">
                    <div class="connection-status">
                        <div class="status-dot"></div>
                        <span>Онлайн</span>
                    </div>
                    <div class="user-avatar" id="user-avatar">U</div>
                </div>
            </div>

            <!-- Mobile Navigation -->
            <nav class="mobile-nav" id="mobile-nav">
                <a class="mobile-nav-link active" data-page="dashboard">Панель</a>
                <a class="mobile-nav-link" data-page="products">Товары</a>
                <a class="mobile-nav-link" data-page="sales">Продажи</a>
                <a class="mobile-nav-link" data-page="income">Поступления</a>
                <a class="mobile-nav-link" data-page="reports">Отчеты</a>
            </nav>
        </header>

        <!-- Main Content -->
        <main class="main">
            <!-- Dashboard Section -->
            <section id="dashboard-section" class="page-section active">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Панель управления</h1>
                    <p class="dashboard-subtitle">Обзор состояния склада и быстрые действия</p>
                </div>

                <!-- Statistics -->
                <div class="stats-grid">
                    <div class="stat-card blue">
                        <div class="stat-header">
                            <div class="stat-title">Товары</div>
                            <div class="stat-icon">📦</div>
                        </div>
                        <div class="stat-value" id="products-count">1,234</div>
                        <div class="stat-change positive">+12% за месяц</div>
                    </div>

                    <div class="stat-card green">
                        <div class="stat-header">
                            <div class="stat-title">Продажи</div>
                            <div class="stat-icon">💰</div>
                        </div>
                        <div class="stat-value" id="sales-amount">₽45,678</div>
                        <div class="stat-change positive">+8% за неделю</div>
                    </div>

                    <div class="stat-card yellow">
                        <div class="stat-header">
                            <div class="stat-title">Поступления</div>
                            <div class="stat-icon">📥</div>
                        </div>
                        <div class="stat-value" id="income-count">567</div>
                        <div class="stat-change positive">+15% за месяц</div>
                    </div>

                    <div class="stat-card purple">
                        <div class="stat-header">
                            <div class="stat-title">Остатки</div>
                            <div class="stat-icon">📊</div>
                        </div>
                        <div class="stat-value" id="inventory-value">₽123,456</div>
                        <div class="stat-change negative">-3% за неделю</div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h2 class="section-title">Быстрые действия</h2>
                    <div class="actions-grid">
                        <button class="action-btn" onclick="showQuickSale()">
                            <span class="action-icon">💰</span>
                            <div class="action-title">Быстрая продажа</div>
                            <div class="action-desc">Продать товар по штрих-коду</div>
                        </button>

                        <button class="action-btn" onclick="showAddProduct()">
                            <span class="action-icon">➕</span>
                            <div class="action-title">Добавить товар</div>
                            <div class="action-desc">Новый товар в каталог</div>
                        </button>

                        <button class="action-btn" onclick="showIncome()">
                            <span class="action-icon">📥</span>
                            <div class="action-title">Поступление</div>
                            <div class="action-desc">Оприходовать товар</div>
                        </button>

                        <button class="action-btn" onclick="showBarcodeScanner()">
                            <span class="action-icon">📱</span>
                            <div class="action-title">Сканер</div>
                            <div class="action-desc">Сканировать штрих-код</div>
                        </button>

                        <button class="action-btn" onclick="testJS()" style="border-color: #ef4444;">
                            <span class="action-icon">🧪</span>
                            <div class="action-title">Тест JS</div>
                            <div class="action-desc">Проверить JavaScript</div>
                        </button>
                    </div>
                </div>
            </section>
        </main>

        <!-- FAB -->
        <button class="fab" id="fab-btn">+</button>
    </div>

    <script>
        // App State
        let currentUser = null;
        let currentPage = 'dashboard';

        // Quick Actions Functions (defined globally for onclick)
        function showQuickSale() {
            alert('Быстрая продажа: Здесь будет форма для быстрой продажи товара');
            console.log('Quick Sale clicked');
        }

        function showAddProduct() {
            alert('Добавить товар: Здесь будет форма добавления нового товара');
            console.log('Add Product clicked');
        }

        function showIncome() {
            alert('Поступление: Здесь будет форма оприходования товара');
            console.log('Income clicked');
        }

        function showBarcodeScanner() {
            alert('Сканер штрих-кода: Здесь будет интерфейс сканирования');
            console.log('Barcode Scanner clicked');
        }

        function showFabMenu() {
            const actions = [
                'Быстрая продажа',
                'Добавить товар',
                'Поступление товара',
                'Сканировать штрих-код'
            ];

            const choice = prompt('Выберите действие:\n' + actions.map((action, i) => `${i + 1}. ${action}`).join('\n'));

            if (choice) {
                const index = parseInt(choice) - 1;
                if (index >= 0 && index < actions.length) {
                    switch (index) {
                        case 0: showQuickSale(); break;
                        case 1: showAddProduct(); break;
                        case 2: showIncome(); break;
                        case 3: showBarcodeScanner(); break;
                    }
                }
            }
        }

        // Test function
        function testJS() {
            alert('JavaScript работает!');
            console.log('Test function called');
        }

        // Initialize App
        document.addEventListener('DOMContentLoaded', function () {
            console.log('Warehouse Management PWA loaded');
            console.log('DOM Content Loaded - App initializing...');

            // Show loading screen for 1.5 seconds
            setTimeout(() => {
                const loadingScreen = document.getElementById('loading-screen');
                const loginScreen = document.getElementById('login-screen');

                console.log('Loading screen element:', loadingScreen);
                console.log('Login screen element:', loginScreen);

                if (loadingScreen) {
                    loadingScreen.classList.add('hidden');
                    console.log('Loading screen hidden');
                }

                if (loginScreen) {
                    loginScreen.classList.remove('hidden');
                    console.log('Login screen shown');
                }
            }, 1500);

            // Login form handler
            const loginForm = document.getElementById('login-form');
            if (loginForm) {
                loginForm.addEventListener('submit', handleLogin);
            }

            // Navigation handlers
            setupNavigation();

            // Mobile menu handler
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', toggleMobileMenu);
            }

            // FAB handler
            const fabBtn = document.getElementById('fab-btn');
            if (fabBtn) {
                fabBtn.addEventListener('click', showFabMenu);
            }

            // Register Service Worker
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('sw.js')
                    .then(registration => console.log('SW registered:', registration))
                    .catch(error => console.log('SW registration failed:', error));
            }
        });

        // Login Handler
        function handleLogin(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username && password) {
                currentUser = { username, name: username };

                // Hide login, show app
                document.getElementById('login-screen').classList.add('hidden');
                document.getElementById('app').classList.remove('hidden');

                // Update user avatar
                document.getElementById('user-avatar').textContent = username.charAt(0).toUpperCase();

                // Load dashboard data
                loadDashboardData();
            } else {
                showLoginError('Пожалуйста, введите имя пользователя и пароль');
            }
        }

        function showLoginError(message) {
            const errorDiv = document.getElementById('login-error');
            const errorMessage = document.getElementById('login-error-message');
            errorMessage.textContent = message;
            errorDiv.classList.remove('hidden');
        }

        // Navigation
        function setupNavigation() {
            // Desktop navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const page = link.getAttribute('data-page');
                    navigateToPage(page);
                });
            });

            // Mobile navigation
            document.querySelectorAll('.mobile-nav-link').forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const page = link.getAttribute('data-page');
                    navigateToPage(page);
                    toggleMobileMenu(); // Close mobile menu
                });
            });
        }

        function navigateToPage(page) {
            // Update active states
            document.querySelectorAll('.nav-link, .mobile-nav-link').forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('data-page') === page) {
                    link.classList.add('active');
                }
            });

            // Hide all sections
            document.querySelectorAll('.page-section').forEach(section => {
                section.classList.remove('active');
            });

            // Show target section
            const targetSection = document.getElementById(page + '-section');
            if (targetSection) {
                targetSection.classList.add('active');
            } else {
                // Create section if it doesn't exist
                createPageSection(page);
            }

            currentPage = page;
        }

        function createPageSection(page) {
            const main = document.querySelector('.main');
            const section = document.createElement('section');
            section.id = page + '-section';
            section.className = 'page-section active';

            let content = '';

            switch (page) {
                case 'products':
                    content = `
                        <h1 class="dashboard-title">Управление товарами</h1>
                        <p class="dashboard-subtitle">Каталог товаров и управление остатками</p>
                        <div style="background: white; padding: 24px; border-radius: 12px; margin-top: 24px;">
                            <p>Здесь будет список товаров...</p>
                        </div>
                    `;
                    break;
                case 'sales':
                    content = `
                        <h1 class="dashboard-title">Продажи</h1>
                        <p class="dashboard-subtitle">История продаж и быстрая продажа</p>
                        <div style="background: white; padding: 24px; border-radius: 12px; margin-top: 24px;">
                            <p>Здесь будет история продаж...</p>
                        </div>
                    `;
                    break;
                case 'income':
                    content = `
                        <h1 class="dashboard-title">Поступления</h1>
                        <p class="dashboard-subtitle">Оприходование товаров на склад</p>
                        <div style="background: white; padding: 24px; border-radius: 12px; margin-top: 24px;">
                            <p>Здесь будет форма поступлений...</p>
                        </div>
                    `;
                    break;
                case 'reports':
                    content = `
                        <h1 class="dashboard-title">Отчеты</h1>
                        <p class="dashboard-subtitle">Аналитика и отчеты по складу</p>
                        <div style="background: white; padding: 24px; border-radius: 12px; margin-top: 24px;">
                            <p>Здесь будут отчеты...</p>
                        </div>
                    `;
                    break;
                default:
                    content = `
                        <h1 class="dashboard-title">Страница не найдена</h1>
                        <p class="dashboard-subtitle">Запрашиваемая страница не существует</p>
                    `;
            }

            section.innerHTML = content;
            main.appendChild(section);
        }

        // Mobile Menu
        function toggleMobileMenu() {
            const mobileNav = document.getElementById('mobile-nav');
            mobileNav.classList.toggle('active');
        }

        // Dashboard Data
        function loadDashboardData() {
            // Simulate loading data
            setTimeout(() => {
                updateStats();
            }, 500);
        }

        function updateStats() {
            // Simulate real-time data updates
            const stats = {
                products: Math.floor(Math.random() * 1000) + 1000,
                sales: Math.floor(Math.random() * 50000) + 30000,
                income: Math.floor(Math.random() * 500) + 400,
                inventory: Math.floor(Math.random() * 100000) + 100000
            };

            document.getElementById('products-count').textContent = stats.products.toLocaleString();
            document.getElementById('sales-amount').textContent = '₽' + stats.sales.toLocaleString();
            document.getElementById('income-count').textContent = stats.income.toLocaleString();
            document.getElementById('inventory-value').textContent = '₽' + stats.inventory.toLocaleString();
        }


    </script>
</body>

</html>