2025-06-03 17:09:49 [127.0.0.1][-][-][error][yii\web\HttpException:404] yii\base\InvalidRouteException: Unable to resolve the request "api". in D:\OSPanel\domains\warehouse\sklad\vendor\yiisoft\yii2\base\Module.php:561
Stack trace:
#0 D:\OSPanel\domains\warehouse\sklad\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api', Array)
#1 D:\OSPanel\domains\warehouse\sklad\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#2 D:\OSPanel\domains\warehouse\sklad\web\index.php(12): yii\base\Application->run()
#3 {main}

Next yii\web\NotFoundHttpException: Page not found. in D:\OSPanel\domains\warehouse\sklad\vendor\yiisoft\yii2\web\Application.php:115
Stack trace:
#0 D:\OSPanel\domains\warehouse\sklad\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#1 D:\OSPanel\domains\warehouse\sklad\web\index.php(12): yii\base\Application->run()
#2 {main}
2025-06-03 17:09:49 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\sklad\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '59345'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\sklad\\web\\index.php'
    'PATH_INFO' => '/api'
    'PHP_SELF' => '/index.php/api'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT; Windows NT 10.0; ru-RU) WindowsPowerShell/5.1.26100.4061'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'Keep-Alive'
    'REQUEST_TIME_FLOAT' => 1748970589.3646
    'REQUEST_TIME' => 1748970589
]
2025-06-03 17:20:29 [-][-][-][error][yii\console\Exception] yii\console\Exception: Unknown option: --limit. Options available: --color, --interactive, --help, --silentExitOnException, --migrationPath, --migrationNamespaces, --compact, --migrationTable, --db in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\console\Controller.php:171
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction('history', Array)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction('migrate/history', Array)
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction('migrate/history', Array)
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest(Object(yii\console\Request))
#4 D:\OSPanel\domains\warehouse\yii(20): yii\base\Application->run()
#5 {main}
2025-06-03 17:20:29 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'c++' => 'C:\\MinGW\\bin'
    'ChocolateyInstall' => 'C:\\ProgramData\\chocolatey'
    'ChocolateyLastPathUpdate' => '133728599031302925'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_18696_YVZUWBOBGBZSUQCK'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'WIN-QPO53K8FFA4'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CURSOR_TRACE_ID' => '504e2ac837ef4a2e8360486aadbe4d80'
    'DataGrip' => 'D:\\app\\DataGrip 2024.1.3\\bin;'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_10372_1592913036' => '1'
    'EFC_10372_2283032206' => '1'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'git' => 'D:\\OSPanel\\Git\\bin'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\user'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\WIN-QPO53K8FFA4'
    'nodejs' => 'C:\\Program Files\\nodejs\\node_modules\\npm\\bin'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;D:\\OSPanel\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;D:\\app\\DataGrip 2024.1.3\\bin;D:\\php\\OpenSSL-Win64\\bin;D:\\ngrok;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\PostgreSQL\\16\\bin;C:\\Program Files\\PowerShell\\7-preview\\preview;C:\\Users\\<USER>\\.fly\\bin;;C:\\Program Files\\Docker\\Docker\\resources\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\php;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;D:\\OSPanel\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;D:\\app\\DataGrip 2024.1.3\\bin;D:\\php\\OpenSSL-Win64\\bin;D:\\ngrok;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:;C:\\Program Files\\PostgreSQL\\16\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.lmstudio\\bin;D:\\chrome data\\Windsurf Next\\bin;C:\\Users\\<USER>\\.fly\\bin;C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Toolbox\\scripts;D:\\Microsoft VS Code\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL'
    'php' => 'C:\\php'
    'PhpStorm' => 'C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;'
    'POWERSHELL_DISTRIBUTION_CHANNEL' => 'MSI:Windows 10 Pro'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 154 Stepping 4, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => '9a04'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'WIN-QPO53K8FFA4'
    'USERDOMAIN_ROAMINGPROFILE' => 'WIN-QPO53K8FFA4'
    'USERNAME' => 'user'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Program Files\\Oracle\\VirtualBox\\'
    'windir' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    'GIT_PAGER' => ''
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '0.51.1'
    'LANG' => 'en_US.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-3505b024ba-sock'
    'VSCODE_INJECTION' => '1'
    'VSCODE_NONCE' => 'a65ce192-5b9a-4968-8e3b-fdeab7de0bb4'
    'VSCODE_STABLE' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1748971229.8365
    'REQUEST_TIME' => 1748971229
    'argv' => [
        0 => 'yii'
        1 => 'migrate/history'
        2 => '--limit=20'
    ]
    'argc' => 3
]
2025-06-03 17:40:12 [127.0.0.1][-][-][error][yii\web\HttpException:404] yii\base\InvalidRouteException: Unable to resolve the request "api". in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php:561
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api', Array)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#2 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#3 {main}

Next yii\web\NotFoundHttpException: Page not found. in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php:115
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#1 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#2 {main}
2025-06-03 17:40:12 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60669'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api'
    'PHP_SELF' => '/index.php/api'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'REQUEST_TIME_FLOAT' => 1748972412.833
    'REQUEST_TIME' => 1748972412
]
2025-06-03 17:43:26 [127.0.0.1][3][-][warning][performance] Slow API request: api/auth/login took 847.50ms (Memory: 2.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:43:25 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60783'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/auth/login'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/auth/login'
    'PHP_SELF' => '/index.php/api/auth/login'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'CONTENT_LENGTH' => '48'
    'HTTP_CONTENT_LENGTH' => '48'
    'REQUEST_TIME_FLOAT' => 1748972605.7708
    'REQUEST_TIME' => 1748972605
]
2025-06-03 17:43:29 [127.0.0.1][3][-][warning][performance] Slow API request: api/products/1 took 208.68ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:43:29 [127.0.0.1][3][-][info][application] $_GET = [
    'id' => '1'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60804'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/products/1'
    'REQUEST_METHOD' => 'DELETE'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/products/1'
    'PHP_SELF' => '/index.php/api/products/1'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748972609.3347
    'REQUEST_TIME' => 1748972609
]
2025-06-03 17:43:31 [127.0.0.1][3][-][warning][performance] Slow API request: api/categories/1 took 277.01ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:43:30 [127.0.0.1][3][-][info][application] $_GET = [
    'id' => '1'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60813'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/categories/1'
    'REQUEST_METHOD' => 'DELETE'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/categories/1'
    'PHP_SELF' => '/index.php/api/categories/1'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748972610.787
    'REQUEST_TIME' => 1748972610
]
2025-06-03 17:43:31 [127.0.0.1][3][-][error][yii\db\Exception] PDOException: SQLSTATE[HY093]: Invalid parameter number: :exact_query in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php:327
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(327): PDOStatement->bindValue(':exact_query', 'test', 2)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(268): yii\db\Command->bindPendingParams()
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal('fetchAll', NULL)
#4 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#5 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all(NULL)
#6 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(47): yii\db\ActiveQuery->all()
#7 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(59): app\services\ProductSearchService->quickSearch('test', 5)
#8 D:\OSPanel\domains\warehouse\controllers\api\QuickIncomeController.php(241): app\services\ProductSearchService->searchForIncome('test', 5)
#9 [internal function]: app\controllers\api\QuickIncomeController->actionSearch()
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#12 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('search', Array)
#13 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/quick-incom...', Array)
#14 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#15 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[HY093]: Invalid parameter number: :exact_query
Failed to prepare SQL: SELECT * FROM "product" WHERE ("name" LIKE :qp1) OR ("barcode" LIKE :qp2) OR ("description" LIKE :qp3) ORDER BY "CASE WHEN name = :exact_query THEN 1 ELSE 2 END", "CASE WHEN barcode = :exact_query THEN 1 ELSE 2 END", "name" LIMIT 5 in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php:272
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal('fetchAll', NULL)
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all(NULL)
#4 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(47): yii\db\ActiveQuery->all()
#5 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(59): app\services\ProductSearchService->quickSearch('test', 5)
#6 D:\OSPanel\domains\warehouse\controllers\api\QuickIncomeController.php(241): app\services\ProductSearchService->searchForIncome('test', 5)
#7 [internal function]: app\controllers\api\QuickIncomeController->actionSearch()
#8 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#9 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('search', Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/quick-incom...', Array)
#12 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#13 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#14 {main}
Additional Information:
Array
(
    [0] => HY093
    [1] => 
    [2] => :exact_query
)

2025-06-03 17:43:31 [127.0.0.1][3][-][info][application] $_GET = [
    'q' => 'test'
    'limit' => '5'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60817'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/income/search?q=test&limit=5'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/income/search'
    'PHP_SELF' => '/index.php/api/income/search'
    'QUERY_STRING' => 'q=test&limit=5'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748972611.4469
    'REQUEST_TIME' => 1748972611
]
2025-06-03 17:43:32 [127.0.0.1][3][-][error][yii\db\Exception] PDOException: SQLSTATE[HY093]: Invalid parameter number: :exact_query in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php:327
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(327): PDOStatement->bindValue(':exact_query', 'test', 2)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(268): yii\db\Command->bindPendingParams()
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal('fetchAll', NULL)
#4 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#5 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all(NULL)
#6 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(47): yii\db\ActiveQuery->all()
#7 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(79): app\services\ProductSearchService->quickSearch('test', 5)
#8 D:\OSPanel\domains\warehouse\controllers\api\QuickSaleController.php(215): app\services\ProductSearchService->searchForSale('test', 5)
#9 [internal function]: app\controllers\api\QuickSaleController->actionSearch()
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#12 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('search', Array)
#13 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/quick-sale/...', Array)
#14 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#15 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[HY093]: Invalid parameter number: :exact_query
Failed to prepare SQL: SELECT * FROM "product" WHERE ("name" LIKE :qp1) OR ("barcode" LIKE :qp2) OR ("description" LIKE :qp3) ORDER BY "CASE WHEN name = :exact_query THEN 1 ELSE 2 END", "CASE WHEN barcode = :exact_query THEN 1 ELSE 2 END", "name" LIMIT 5 in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php:272
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal('fetchAll', NULL)
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all(NULL)
#4 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(47): yii\db\ActiveQuery->all()
#5 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(79): app\services\ProductSearchService->quickSearch('test', 5)
#6 D:\OSPanel\domains\warehouse\controllers\api\QuickSaleController.php(215): app\services\ProductSearchService->searchForSale('test', 5)
#7 [internal function]: app\controllers\api\QuickSaleController->actionSearch()
#8 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#9 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('search', Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/quick-sale/...', Array)
#12 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#13 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#14 {main}
Additional Information:
Array
(
    [0] => HY093
    [1] => 
    [2] => :exact_query
)

2025-06-03 17:43:31 [127.0.0.1][3][-][info][application] $_GET = [
    'q' => 'test'
    'limit' => '5'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60820'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/sales/search?q=test&limit=5'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/sales/search'
    'PHP_SELF' => '/index.php/api/sales/search'
    'QUERY_STRING' => 'q=test&limit=5'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748972611.9124
    'REQUEST_TIME' => 1748972611
]
2025-06-03 17:45:24 [127.0.0.1][3][-][warning][performance] Slow API request: api/auth/login took 815.53ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:45:23 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60871'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/auth/login'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/auth/login'
    'PHP_SELF' => '/index.php/api/auth/login'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'CONTENT_LENGTH' => '48'
    'HTTP_CONTENT_LENGTH' => '48'
    'REQUEST_TIME_FLOAT' => 1748972723.9632
    'REQUEST_TIME' => 1748972723
]
2025-06-03 17:45:25 [127.0.0.1][3][-][warning][performance] Slow API request: api/products took 338.72ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:45:25 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60877'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/products'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/products'
    'PHP_SELF' => '/index.php/api/products'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'CONTENT_LENGTH' => '130'
    'HTTP_CONTENT_LENGTH' => '130'
    'REQUEST_TIME_FLOAT' => 1748972725.5765
    'REQUEST_TIME' => 1748972725
]
2025-06-03 17:45:26 [127.0.0.1][3][-][error][application] Income processing failed: Getting unknown property: app\models\Income::total_amount
    in D:\OSPanel\domains\warehouse\services\InventoryService.php:55
    in D:\OSPanel\domains\warehouse\controllers\api\QuickIncomeController.php:88
2025-06-03 17:45:26 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60882'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/income/scan-barcode'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/income/scan-barcode'
    'PHP_SELF' => '/index.php/api/income/scan-barcode'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'CONTENT_LENGTH' => '100'
    'HTTP_CONTENT_LENGTH' => '100'
    'REQUEST_TIME_FLOAT' => 1748972726.2506
    'REQUEST_TIME' => 1748972726
]
2025-06-03 17:45:26 [127.0.0.1][3][-][error][application] Income processing failed: Getting unknown property: app\models\Income::total_amount
    in D:\OSPanel\domains\warehouse\services\InventoryService.php:55
    in D:\OSPanel\domains\warehouse\controllers\api\QuickIncomeController.php:161
2025-06-03 17:45:26 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60885'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/income/quick-entry'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/income/quick-entry'
    'PHP_SELF' => '/index.php/api/income/quick-entry'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'CONTENT_LENGTH' => '91'
    'HTTP_CONTENT_LENGTH' => '91'
    'REQUEST_TIME_FLOAT' => 1748972726.7935
    'REQUEST_TIME' => 1748972726
]
2025-06-03 17:45:27 [127.0.0.1][3][-][error][yii\db\Exception] PDOException: SQLSTATE[HY093]: Invalid parameter number: :exact_query in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php:327
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(327): PDOStatement->bindValue(':exact_query', 'Test', 2)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(268): yii\db\Command->bindPendingParams()
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal('fetchAll', NULL)
#4 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#5 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all(NULL)
#6 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(47): yii\db\ActiveQuery->all()
#7 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(59): app\services\ProductSearchService->quickSearch('Test', 5)
#8 D:\OSPanel\domains\warehouse\controllers\api\QuickIncomeController.php(241): app\services\ProductSearchService->searchForIncome('Test', 5)
#9 [internal function]: app\controllers\api\QuickIncomeController->actionSearch()
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#12 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('search', Array)
#13 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/quick-incom...', Array)
#14 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#15 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[HY093]: Invalid parameter number: :exact_query
Failed to prepare SQL: SELECT * FROM "product" WHERE ("name" LIKE :qp1) OR ("barcode" LIKE :qp2) OR ("description" LIKE :qp3) ORDER BY "CASE WHEN name = :exact_query THEN 1 ELSE 2 END", "CASE WHEN barcode = :exact_query THEN 1 ELSE 2 END", "name" LIMIT 5 in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php:272
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal('fetchAll', NULL)
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all(NULL)
#4 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(47): yii\db\ActiveQuery->all()
#5 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(59): app\services\ProductSearchService->quickSearch('Test', 5)
#6 D:\OSPanel\domains\warehouse\controllers\api\QuickIncomeController.php(241): app\services\ProductSearchService->searchForIncome('Test', 5)
#7 [internal function]: app\controllers\api\QuickIncomeController->actionSearch()
#8 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#9 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('search', Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/quick-incom...', Array)
#12 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#13 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#14 {main}
Additional Information:
Array
(
    [0] => HY093
    [1] => 
    [2] => :exact_query
)

2025-06-03 17:45:27 [127.0.0.1][3][-][info][application] $_GET = [
    'q' => 'Test'
    'limit' => '5'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60888'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/income/search?q=Test&limit=5'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/income/search'
    'PHP_SELF' => '/index.php/api/income/search'
    'QUERY_STRING' => 'q=Test&limit=5'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748972727.2691
    'REQUEST_TIME' => 1748972727
]
2025-06-03 17:46:44 [127.0.0.1][3][-][warning][performance] Slow API request: api/auth/login took 882.52ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:46:43 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60938'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/auth/login'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/auth/login'
    'PHP_SELF' => '/index.php/api/auth/login'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'CONTENT_LENGTH' => '48'
    'HTTP_CONTENT_LENGTH' => '48'
    'REQUEST_TIME_FLOAT' => 1748972803.2911
    'REQUEST_TIME' => 1748972803
]
2025-06-03 17:46:45 [127.0.0.1][3][-][warning][performance] Slow API request: api/sales/scan-barcode took 262.79ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:46:45 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60947'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/sales/scan-barcode'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/sales/scan-barcode'
    'PHP_SELF' => '/index.php/api/sales/scan-barcode'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'CONTENT_LENGTH' => '97'
    'HTTP_CONTENT_LENGTH' => '97'
    'REQUEST_TIME_FLOAT' => 1748972805.4962
    'REQUEST_TIME' => 1748972805
]
2025-06-03 17:46:46 [127.0.0.1][3][-][error][yii\db\Exception] PDOException: SQLSTATE[HY093]: Invalid parameter number: :exact_query in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php:327
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(327): PDOStatement->bindValue(':exact_query', 'Test', 2)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(268): yii\db\Command->bindPendingParams()
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal('fetchAll', NULL)
#4 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#5 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all(NULL)
#6 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(47): yii\db\ActiveQuery->all()
#7 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(79): app\services\ProductSearchService->quickSearch('Test', 5)
#8 D:\OSPanel\domains\warehouse\controllers\api\QuickSaleController.php(215): app\services\ProductSearchService->searchForSale('Test', 5)
#9 [internal function]: app\controllers\api\QuickSaleController->actionSearch()
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#12 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('search', Array)
#13 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/quick-sale/...', Array)
#14 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#15 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[HY093]: Invalid parameter number: :exact_query
Failed to prepare SQL: SELECT * FROM "product" WHERE ("name" LIKE :qp1) OR ("barcode" LIKE :qp2) OR ("description" LIKE :qp3) ORDER BY "CASE WHEN name = :exact_query THEN 1 ELSE 2 END", "CASE WHEN barcode = :exact_query THEN 1 ELSE 2 END", "name" LIMIT 5 in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php:272
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal('fetchAll', NULL)
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all(NULL)
#4 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(47): yii\db\ActiveQuery->all()
#5 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(79): app\services\ProductSearchService->quickSearch('Test', 5)
#6 D:\OSPanel\domains\warehouse\controllers\api\QuickSaleController.php(215): app\services\ProductSearchService->searchForSale('Test', 5)
#7 [internal function]: app\controllers\api\QuickSaleController->actionSearch()
#8 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#9 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('search', Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/quick-sale/...', Array)
#12 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#13 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#14 {main}
Additional Information:
Array
(
    [0] => HY093
    [1] => 
    [2] => :exact_query
)

2025-06-03 17:46:46 [127.0.0.1][3][-][info][application] $_GET = [
    'q' => 'Test'
    'limit' => '5'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60955'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/sales/search?q=Test&limit=5'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/sales/search'
    'PHP_SELF' => '/index.php/api/sales/search'
    'QUERY_STRING' => 'q=Test&limit=5'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748972806.586
    'REQUEST_TIME' => 1748972806
]
2025-06-03 17:47:56 [127.0.0.1][3][-][warning][performance] Slow API request: api/auth/login took 823.91ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:47:55 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60996'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/auth/login'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/auth/login'
    'PHP_SELF' => '/index.php/api/auth/login'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'CONTENT_LENGTH' => '48'
    'HTTP_CONTENT_LENGTH' => '48'
    'REQUEST_TIME_FLOAT' => 1748972875.8943
    'REQUEST_TIME' => 1748972875
]
2025-06-03 17:47:58 [127.0.0.1][3][-][warning][performance] Slow API request: api/sales/scan-barcode took 226.97ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:47:58 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61005'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/sales/scan-barcode'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/sales/scan-barcode'
    'PHP_SELF' => '/index.php/api/sales/scan-barcode'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'CONTENT_LENGTH' => '92'
    'HTTP_CONTENT_LENGTH' => '92'
    'REQUEST_TIME_FLOAT' => 1748972878.049
    'REQUEST_TIME' => 1748972878
]
2025-06-03 17:47:58 [127.0.0.1][3][-][warning][performance] Slow API request: api/receipts/generate took 244.53ms (Memory: 2.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:47:58 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61008'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/receipts/generate'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/receipts/generate'
    'PHP_SELF' => '/index.php/api/receipts/generate'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'CONTENT_LENGTH' => '124'
    'HTTP_CONTENT_LENGTH' => '124'
    'REQUEST_TIME_FLOAT' => 1748972878.5932
    'REQUEST_TIME' => 1748972878
]
2025-06-03 17:47:59 [127.0.0.1][3][-][warning][performance] Slow API request: api/receipts/1/print took 204.59ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:47:59 [127.0.0.1][3][-][info][application] $_GET = [
    'id' => '1'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61016'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/receipts/1/print'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/receipts/1/print'
    'PHP_SELF' => '/index.php/api/receipts/1/print'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'CONTENT_LENGTH' => '43'
    'HTTP_CONTENT_LENGTH' => '43'
    'REQUEST_TIME_FLOAT' => 1748972879.5972
    'REQUEST_TIME' => 1748972879
]
2025-06-03 17:48:01 [127.0.0.1][3][-][error][yii\db\IntegrityException] PDOException: SQLSTATE[23503]: Foreign key violation: 7 ERROR:  update or delete on table "product" violates foreign key constraint "fk-receipt-item-product_id" on table "receipt_item"
DETAIL:  Key (id)=(4) is still referenced from table "receipt_item". in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1120): yii\db\Command->internalExecute('DELETE FROM "pr...')
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveRecord.php(405): yii\db\Command->execute()
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveRecord.php(765): yii\db\ActiveRecord::deleteAll(Array)
#4 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveRecord.php(724): yii\db\ActiveRecord->deleteInternal()
#5 D:\OSPanel\domains\warehouse\controllers\api\ProductController.php(120): yii\db\ActiveRecord->delete()
#6 [internal function]: app\controllers\api\ProductController->actionDelete('4')
#7 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#8 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#9 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('delete', Array)
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/product/del...', Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#12 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#13 {main}

Next yii\db\IntegrityException: SQLSTATE[23503]: Foreign key violation: 7 ERROR:  update or delete on table "product" violates foreign key constraint "fk-receipt-item-product_id" on table "receipt_item"
DETAIL:  Key (id)=(4) is still referenced from table "receipt_item".
The SQL being executed was: DELETE FROM "product" WHERE "id"=4 in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException(Object(PDOException), 'DELETE FROM "pr...')
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1120): yii\db\Command->internalExecute('DELETE FROM "pr...')
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveRecord.php(405): yii\db\Command->execute()
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveRecord.php(765): yii\db\ActiveRecord::deleteAll(Array)
#4 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveRecord.php(724): yii\db\ActiveRecord->deleteInternal()
#5 D:\OSPanel\domains\warehouse\controllers\api\ProductController.php(120): yii\db\ActiveRecord->delete()
#6 [internal function]: app\controllers\api\ProductController->actionDelete('4')
#7 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#8 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#9 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('delete', Array)
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/product/del...', Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#12 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#13 {main}
Additional Information:
Array
(
    [0] => 23503
    [1] => 7
    [2] => ERROR:  update or delete on table "product" violates foreign key constraint "fk-receipt-item-product_id" on table "receipt_item"
DETAIL:  Key (id)=(4) is still referenced from table "receipt_item".
)

2025-06-03 17:48:01 [127.0.0.1][3][-][info][application] $_GET = [
    'id' => '4'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61026'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/products/4'
    'REQUEST_METHOD' => 'DELETE'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/products/4'
    'PHP_SELF' => '/index.php/api/products/4'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748972881.1122
    'REQUEST_TIME' => 1748972881
]
2025-06-03 17:49:09 [127.0.0.1][3][-][error][performance] Slow API request: api/auth/login took 1801.52ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:106
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:49:08 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61112'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/auth/login'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/auth/login'
    'PHP_SELF' => '/index.php/api/auth/login'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'CONTENT_LENGTH' => '48'
    'HTTP_CONTENT_LENGTH' => '48'
    'REQUEST_TIME_FLOAT' => 1748972948.0061
    'REQUEST_TIME' => 1748972948
]
2025-06-03 17:49:10 [127.0.0.1][3][-][error][yii\base\UnknownPropertyException] yii\base\UnknownPropertyException: Getting unknown property: app\models\Product::sku in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Component.php:154
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\BaseActiveRecord.php(296): yii\base\Component->__get('sku')
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\helpers\BaseArrayHelper.php(82): yii\db\BaseActiveRecord->__get('sku')
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\helpers\BaseArrayHelper.php(67): yii\helpers\BaseArrayHelper::toArray(Object(app\models\Product), Array, true)
#3 D:\OSPanel\domains\warehouse\controllers\api\ReportController.php(51): yii\helpers\BaseArrayHelper::toArray(Array, Array)
#4 [internal function]: app\controllers\api\ReportController->actionStock()
#5 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#6 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#7 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('stock', Array)
#8 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/report/stoc...', Array)
#9 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#10 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#11 {main}
2025-06-03 17:49:10 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61115'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/reports/stock'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/reports/stock'
    'PHP_SELF' => '/index.php/api/reports/stock'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748972950.2613
    'REQUEST_TIME' => 1748972950
]
2025-06-03 17:49:11 [127.0.0.1][3][-][error][yii\base\UnknownPropertyException] yii\base\UnknownPropertyException: Getting unknown property: app\models\Product::sku in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Component.php:154
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\BaseActiveRecord.php(296): yii\base\Component->__get('sku')
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\helpers\BaseArrayHelper.php(82): yii\db\BaseActiveRecord->__get('sku')
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\helpers\BaseArrayHelper.php(67): yii\helpers\BaseArrayHelper::toArray(Object(app\models\Product), Array, true)
#3 D:\OSPanel\domains\warehouse\controllers\api\ReportController.php(51): yii\helpers\BaseArrayHelper::toArray(Array, Array)
#4 [internal function]: app\controllers\api\ReportController->actionStock()
#5 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#6 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#7 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('stock', Array)
#8 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/report/stoc...', Array)
#9 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#10 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#11 {main}
2025-06-03 17:49:11 [127.0.0.1][3][-][info][application] $_GET = [
    'low_stock' => '5'
    'limit' => '10'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61118'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/reports/stock?low_stock=5&limit=10'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/reports/stock'
    'PHP_SELF' => '/index.php/api/reports/stock'
    'QUERY_STRING' => 'low_stock=5&limit=10'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748972951.1157
    'REQUEST_TIME' => 1748972951
]
2025-06-03 17:49:12 [127.0.0.1][3][-][error][yii\base\UnknownPropertyException] yii\base\UnknownPropertyException: Getting unknown property: app\models\Product::sku in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Component.php:154
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\BaseActiveRecord.php(296): yii\base\Component->__get('sku')
#1 D:\OSPanel\domains\warehouse\controllers\api\ReportController.php(90): yii\db\BaseActiveRecord->__get('sku')
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\helpers\BaseArrayHelper.php(190): app\controllers\api\ReportController->app\controllers\api\{closure}(Object(app\models\Sale), NULL)
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\helpers\BaseArrayHelper.php(84): yii\helpers\BaseArrayHelper::getValue(Object(app\models\Sale), Object(Closure))
#4 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\helpers\BaseArrayHelper.php(67): yii\helpers\BaseArrayHelper::toArray(Object(app\models\Sale), Array, true)
#5 D:\OSPanel\domains\warehouse\controllers\api\ReportController.php(91): yii\helpers\BaseArrayHelper::toArray(Array, Array)
#6 [internal function]: app\controllers\api\ReportController->actionSales(NULL, NULL)
#7 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#8 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#9 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('sales', Array)
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/report/sale...', Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#12 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#13 {main}
2025-06-03 17:49:12 [127.0.0.1][3][-][info][application] $_GET = [
    'start_date' => '2025-01-01'
    'end_date' => '2025-12-31'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61123'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/reports/sales?start_date=2025-01-01&end_date=2025-12-31'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/reports/sales'
    'PHP_SELF' => '/index.php/api/reports/sales'
    'QUERY_STRING' => 'start_date=2025-01-01&end_date=2025-12-31'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748972952.0163
    'REQUEST_TIME' => 1748972952
]
2025-06-03 17:49:13 [127.0.0.1][3][-][error][yii\base\UnknownPropertyException] yii\base\UnknownPropertyException: Getting unknown property: app\models\Product::sku in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Component.php:154
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\BaseActiveRecord.php(296): yii\base\Component->__get('sku')
#1 D:\OSPanel\domains\warehouse\controllers\api\ReportController.php(90): yii\db\BaseActiveRecord->__get('sku')
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\helpers\BaseArrayHelper.php(190): app\controllers\api\ReportController->app\controllers\api\{closure}(Object(app\models\Sale), NULL)
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\helpers\BaseArrayHelper.php(84): yii\helpers\BaseArrayHelper::getValue(Object(app\models\Sale), Object(Closure))
#4 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\helpers\BaseArrayHelper.php(67): yii\helpers\BaseArrayHelper::toArray(Object(app\models\Sale), Array, true)
#5 D:\OSPanel\domains\warehouse\controllers\api\ReportController.php(91): yii\helpers\BaseArrayHelper::toArray(Array, Array)
#6 [internal function]: app\controllers\api\ReportController->actionSales(NULL, NULL)
#7 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#8 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#9 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('sales', Array)
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/report/sale...', Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#12 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#13 {main}
2025-06-03 17:49:12 [127.0.0.1][3][-][info][application] $_GET = [
    'start_date' => '2025-01-01'
    'end_date' => '2025-12-31'
    'group_by' => 'product'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61126'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/reports/sales?start_date=2025-01-01&end_date=2025-12-31&group_by=product'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/reports/sales'
    'PHP_SELF' => '/index.php/api/reports/sales'
    'QUERY_STRING' => 'start_date=2025-01-01&end_date=2025-12-31&group_by=product'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748972952.8941
    'REQUEST_TIME' => 1748972952
]
2025-06-03 17:49:14 [127.0.0.1][3][-][warning][performance] Slow API request: api/reports/income took 275.27ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:49:13 [127.0.0.1][3][-][info][application] $_GET = [
    'start_date' => '2025-01-01'
    'end_date' => '2025-12-31'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61130'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/reports/income?start_date=2025-01-01&end_date=2025-12-31'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/reports/income'
    'PHP_SELF' => '/index.php/api/reports/income'
    'QUERY_STRING' => 'start_date=2025-01-01&end_date=2025-12-31'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748972953.7784
    'REQUEST_TIME' => 1748972953
]
2025-06-03 17:49:14 [][-][-][error][yii\base\ErrorException:2] yii\base\ErrorException: Undefined array key "report_type" in D:\OSPanel\domains\warehouse\test_reports.php:129
Stack trace:
#0 D:\OSPanel\domains\warehouse\test_reports.php(129): yii\base\ErrorHandler->handleError(2, 'Undefined array...', 'D:\\OSPanel\\doma...', 129)
#1 D:\OSPanel\domains\warehouse\test_reports.php(32): ReportsTest->testIncomeReport()
#2 D:\OSPanel\domains\warehouse\test_reports.php(249): ReportsTest->runTest()
#3 {main}
2025-06-03 17:49:07 [][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'c++' => 'C:\\MinGW\\bin'
    'ChocolateyInstall' => 'C:\\ProgramData\\chocolatey'
    'ChocolateyLastPathUpdate' => '133728599031302925'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_18696_YVZUWBOBGBZSUQCK'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'WIN-QPO53K8FFA4'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CURSOR_TRACE_ID' => '504e2ac837ef4a2e8360486aadbe4d80'
    'DataGrip' => 'D:\\app\\DataGrip 2024.1.3\\bin;'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_10372_1592913036' => '1'
    'EFC_10372_2283032206' => '1'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'git' => 'D:\\OSPanel\\Git\\bin'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\user'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\WIN-QPO53K8FFA4'
    'nodejs' => 'C:\\Program Files\\nodejs\\node_modules\\npm\\bin'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;D:\\OSPanel\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;D:\\app\\DataGrip 2024.1.3\\bin;D:\\php\\OpenSSL-Win64\\bin;D:\\ngrok;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\PostgreSQL\\16\\bin;C:\\Program Files\\PowerShell\\7-preview\\preview;C:\\Users\\<USER>\\.fly\\bin;;C:\\Program Files\\Docker\\Docker\\resources\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\php;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;D:\\OSPanel\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;D:\\app\\DataGrip 2024.1.3\\bin;D:\\php\\OpenSSL-Win64\\bin;D:\\ngrok;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:;C:\\Program Files\\PostgreSQL\\16\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.lmstudio\\bin;D:\\chrome data\\Windsurf Next\\bin;C:\\Users\\<USER>\\.fly\\bin;C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Toolbox\\scripts;D:\\Microsoft VS Code\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL'
    'php' => 'C:\\php'
    'PhpStorm' => 'C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;'
    'POWERSHELL_DISTRIBUTION_CHANNEL' => 'MSI:Windows 10 Pro'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 154 Stepping 4, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => '9a04'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'WIN-QPO53K8FFA4'
    'USERDOMAIN_ROAMINGPROFILE' => 'WIN-QPO53K8FFA4'
    'USERNAME' => 'user'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Program Files\\Oracle\\VirtualBox\\'
    'windir' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    'GIT_PAGER' => ''
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '0.51.1'
    'LANG' => 'en_US.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-3505b024ba-sock'
    'VSCODE_INJECTION' => '1'
    'VSCODE_NONCE' => 'ff12b92e-e7c7-4722-a63d-f5f1c47bd33d'
    'VSCODE_STABLE' => '1'
    'PHP_SELF' => 'test_reports.php'
    'SCRIPT_NAME' => 'test_reports.php'
    'SCRIPT_FILENAME' => 'test_reports.php'
    'PATH_TRANSLATED' => 'test_reports.php'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1748972947.7484
    'REQUEST_TIME' => 1748972947
    'argv' => [
        0 => 'test_reports.php'
    ]
    'argc' => 1
]
2025-06-03 17:56:08 [127.0.0.1][3][-][warning][performance] Slow API request: api/auth/login took 839.54ms (Memory: 2.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:56:07 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61419'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/auth/login'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/auth/login'
    'PHP_SELF' => '/index.php/api/auth/login'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'CONTENT_LENGTH' => '48'
    'HTTP_CONTENT_LENGTH' => '48'
    'REQUEST_TIME_FLOAT' => 1748973367.7635
    'REQUEST_TIME' => 1748973367
]
2025-06-03 17:56:10 [127.0.0.1][3][-][error][application] Income processing failed: Getting unknown property: app\models\Income::total_amount
    in D:\OSPanel\domains\warehouse\services\InventoryService.php:55
    in D:\OSPanel\domains\warehouse\controllers\api\QuickIncomeController.php:88
2025-06-03 17:56:09 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61430'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/income/scan-barcode'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/income/scan-barcode'
    'PHP_SELF' => '/index.php/api/income/scan-barcode'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'CONTENT_LENGTH' => '100'
    'HTTP_CONTENT_LENGTH' => '100'
    'REQUEST_TIME_FLOAT' => 1748973369.8914
    'REQUEST_TIME' => 1748973369
]
2025-06-03 17:56:10 [127.0.0.1][3][-][error][application] Income processing failed: Getting unknown property: app\models\Income::total_amount
    in D:\OSPanel\domains\warehouse\services\InventoryService.php:55
    in D:\OSPanel\domains\warehouse\controllers\api\QuickIncomeController.php:160
2025-06-03 17:56:10 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61435'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/income/quick-entry'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/income/quick-entry'
    'PHP_SELF' => '/index.php/api/income/quick-entry'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'CONTENT_LENGTH' => '91'
    'HTTP_CONTENT_LENGTH' => '91'
    'REQUEST_TIME_FLOAT' => 1748973370.4151
    'REQUEST_TIME' => 1748973370
]
2025-06-03 17:57:25 [127.0.0.1][3][-][warning][performance] Slow API request: api/auth/login took 830.08ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:57:24 [127.0.0.1][3][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61468'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/auth/login'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/auth/login'
    'PHP_SELF' => '/index.php/api/auth/login'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'CONTENT_LENGTH' => '48'
    'HTTP_CONTENT_LENGTH' => '48'
    'REQUEST_TIME_FLOAT' => 1748973444.3515
    'REQUEST_TIME' => 1748973444
]
2025-06-03 17:57:28 [][-][-][error][yii\base\ErrorException:2] yii\base\ErrorException: Undefined array key "report_type" in D:\OSPanel\domains\warehouse\test_reports.php:152
Stack trace:
#0 D:\OSPanel\domains\warehouse\test_reports.php(152): yii\base\ErrorHandler->handleError(2, 'Undefined array...', 'D:\\OSPanel\\doma...', 152)
#1 D:\OSPanel\domains\warehouse\test_reports.php(35): ReportsTest->testSummaryReport()
#2 D:\OSPanel\domains\warehouse\test_reports.php(249): ReportsTest->runTest()
#3 {main}
2025-06-03 17:57:24 [][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'c++' => 'C:\\MinGW\\bin'
    'ChocolateyInstall' => 'C:\\ProgramData\\chocolatey'
    'ChocolateyLastPathUpdate' => '133728599031302925'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_18696_YVZUWBOBGBZSUQCK'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'WIN-QPO53K8FFA4'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CURSOR_TRACE_ID' => '504e2ac837ef4a2e8360486aadbe4d80'
    'DataGrip' => 'D:\\app\\DataGrip 2024.1.3\\bin;'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_10372_1592913036' => '1'
    'EFC_10372_2283032206' => '1'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'git' => 'D:\\OSPanel\\Git\\bin'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\user'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\WIN-QPO53K8FFA4'
    'nodejs' => 'C:\\Program Files\\nodejs\\node_modules\\npm\\bin'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;D:\\OSPanel\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;D:\\app\\DataGrip 2024.1.3\\bin;D:\\php\\OpenSSL-Win64\\bin;D:\\ngrok;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\PostgreSQL\\16\\bin;C:\\Program Files\\PowerShell\\7-preview\\preview;C:\\Users\\<USER>\\.fly\\bin;;C:\\Program Files\\Docker\\Docker\\resources\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\php;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;D:\\OSPanel\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;D:\\app\\DataGrip 2024.1.3\\bin;D:\\php\\OpenSSL-Win64\\bin;D:\\ngrok;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:;C:\\Program Files\\PostgreSQL\\16\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.lmstudio\\bin;D:\\chrome data\\Windsurf Next\\bin;C:\\Users\\<USER>\\.fly\\bin;C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Toolbox\\scripts;D:\\Microsoft VS Code\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL'
    'php' => 'C:\\php'
    'PhpStorm' => 'C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;'
    'POWERSHELL_DISTRIBUTION_CHANNEL' => 'MSI:Windows 10 Pro'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 154 Stepping 4, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => '9a04'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'WIN-QPO53K8FFA4'
    'USERDOMAIN_ROAMINGPROFILE' => 'WIN-QPO53K8FFA4'
    'USERNAME' => 'user'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Program Files\\Oracle\\VirtualBox\\'
    'windir' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    'GIT_PAGER' => ''
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '0.51.1'
    'LANG' => 'en_US.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-3505b024ba-sock'
    'VSCODE_INJECTION' => '1'
    'VSCODE_NONCE' => '9101d1d4-fce8-4b69-b574-2b9b6479df48'
    'VSCODE_STABLE' => '1'
    'PHP_SELF' => 'test_reports.php'
    'SCRIPT_NAME' => 'test_reports.php'
    'SCRIPT_FILENAME' => 'test_reports.php'
    'PATH_TRANSLATED' => 'test_reports.php'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1748973444.0825
    'REQUEST_TIME' => 1748973444
    'argv' => [
        0 => 'test_reports.php'
    ]
    'argc' => 1
]
2025-06-03 17:57:56 [127.0.0.1][4][-][warning][performance] Slow API request: api/auth/login took 831.31ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:57:55 [127.0.0.1][4][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61504'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/auth/login'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/auth/login'
    'PHP_SELF' => '/index.php/api/auth/login'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'CONTENT_LENGTH' => '48'
    'HTTP_CONTENT_LENGTH' => '48'
    'REQUEST_TIME_FLOAT' => 1748973475.702
    'REQUEST_TIME' => 1748973475
]
2025-06-03 17:57:58 [127.0.0.1][4][-][warning][performance] Slow API request: api/products/6 took 283.34ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:57:58 [127.0.0.1][4][-][info][application] $_GET = [
    'id' => '6'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61517'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/products/6'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/products/6'
    'PHP_SELF' => '/index.php/api/products/6'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748973478.1727
    'REQUEST_TIME' => 1748973478
]
2025-06-03 17:57:59 [127.0.0.1][4][-][warning][performance] Slow API request: api/products/6 took 217.21ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:57:58 [127.0.0.1][4][-][info][application] $_GET = [
    'id' => '6'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61521'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/products/6'
    'REQUEST_METHOD' => 'PUT'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/products/6'
    'PHP_SELF' => '/index.php/api/products/6'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'CONTENT_LENGTH' => '31'
    'HTTP_CONTENT_LENGTH' => '31'
    'REQUEST_TIME_FLOAT' => 1748973478.7652
    'REQUEST_TIME' => 1748973478
]
2025-06-03 17:58:01 [127.0.0.1][4][-][error][yii\db\Exception] PDOException: SQLSTATE[HY093]: Invalid parameter number: :exact_query in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php:327
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(327): PDOStatement->bindValue(':exact_query', 'test', 2)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(268): yii\db\Command->bindPendingParams()
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal('fetchAll', NULL)
#4 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#5 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all(NULL)
#6 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(47): yii\db\ActiveQuery->all()
#7 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(79): app\services\ProductSearchService->quickSearch('test', 5)
#8 D:\OSPanel\domains\warehouse\controllers\api\QuickSaleController.php(215): app\services\ProductSearchService->searchForSale('test', 5)
#9 [internal function]: app\controllers\api\QuickSaleController->actionSearch()
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#12 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('search', Array)
#13 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/quick-sale/...', Array)
#14 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#15 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[HY093]: Invalid parameter number: :exact_query
Failed to prepare SQL: SELECT * FROM "product" WHERE ("name" LIKE :qp1) OR ("barcode" LIKE :qp2) OR ("description" LIKE :qp3) ORDER BY "CASE WHEN name = :exact_query THEN 1 ELSE 2 END", "CASE WHEN barcode = :exact_query THEN 1 ELSE 2 END", "name" LIMIT 5 in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php:272
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal('fetchAll', NULL)
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all(NULL)
#4 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(47): yii\db\ActiveQuery->all()
#5 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(79): app\services\ProductSearchService->quickSearch('test', 5)
#6 D:\OSPanel\domains\warehouse\controllers\api\QuickSaleController.php(215): app\services\ProductSearchService->searchForSale('test', 5)
#7 [internal function]: app\controllers\api\QuickSaleController->actionSearch()
#8 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#9 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('search', Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/quick-sale/...', Array)
#12 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#13 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#14 {main}
Additional Information:
Array
(
    [0] => HY093
    [1] => 
    [2] => :exact_query
)

2025-06-03 17:58:01 [127.0.0.1][4][-][info][application] $_GET = [
    'q' => 'test'
    'limit' => '5'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61542'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/sales/search?q=test&limit=5'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/sales/search'
    'PHP_SELF' => '/index.php/api/sales/search'
    'QUERY_STRING' => 'q=test&limit=5'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748973481.6017
    'REQUEST_TIME' => 1748973481
]
2025-06-03 17:58:48 [127.0.0.1][4][-][warning][performance] Slow API request: api/auth/login took 818.37ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:58:47 [127.0.0.1][4][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61568'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/auth/login'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/auth/login'
    'PHP_SELF' => '/index.php/api/auth/login'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'CONTENT_LENGTH' => '48'
    'HTTP_CONTENT_LENGTH' => '48'
    'REQUEST_TIME_FLOAT' => 1748973527.2644
    'REQUEST_TIME' => 1748973527
]
2025-06-03 17:58:50 [127.0.0.1][4][-][warning][performance] Slow API request: api/sales/quick-entry took 208.27ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:58:49 [127.0.0.1][4][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61582'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/sales/quick-entry'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/sales/quick-entry'
    'PHP_SELF' => '/index.php/api/sales/quick-entry'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'CONTENT_LENGTH' => '89'
    'HTTP_CONTENT_LENGTH' => '89'
    'REQUEST_TIME_FLOAT' => 1748973529.8498
    'REQUEST_TIME' => 1748973529
]
2025-06-03 17:58:50 [127.0.0.1][4][-][error][yii\db\Exception] PDOException: SQLSTATE[HY093]: Invalid parameter number: :exact_query in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php:327
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(327): PDOStatement->bindValue(':exact_query', 'Test', 2)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(268): yii\db\Command->bindPendingParams()
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal('fetchAll', NULL)
#4 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#5 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all(NULL)
#6 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(47): yii\db\ActiveQuery->all()
#7 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(79): app\services\ProductSearchService->quickSearch('Test', 5)
#8 D:\OSPanel\domains\warehouse\controllers\api\QuickSaleController.php(215): app\services\ProductSearchService->searchForSale('Test', 5)
#9 [internal function]: app\controllers\api\QuickSaleController->actionSearch()
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#12 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('search', Array)
#13 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/quick-sale/...', Array)
#14 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#15 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[HY093]: Invalid parameter number: :exact_query
Failed to prepare SQL: SELECT * FROM "product" WHERE ("name" LIKE :qp1) OR ("barcode" LIKE :qp2) OR ("description" LIKE :qp3) ORDER BY "CASE WHEN name = :exact_query THEN 1 ELSE 2 END", "CASE WHEN barcode = :exact_query THEN 1 ELSE 2 END", "name" LIMIT 5 in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php:272
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(1181): yii\db\Command->prepare(true)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal('fetchAll', NULL)
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all(NULL)
#4 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(47): yii\db\ActiveQuery->all()
#5 D:\OSPanel\domains\warehouse\services\ProductSearchService.php(79): app\services\ProductSearchService->quickSearch('Test', 5)
#6 D:\OSPanel\domains\warehouse\controllers\api\QuickSaleController.php(215): app\services\ProductSearchService->searchForSale('Test', 5)
#7 [internal function]: app\controllers\api\QuickSaleController->actionSearch()
#8 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array(Array, Array)
#9 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams(Array)
#10 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\base\Controller->runAction('search', Array)
#11 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api/quick-sale/...', Array)
#12 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#13 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#14 {main}
Additional Information:
Array
(
    [0] => HY093
    [1] => 
    [2] => :exact_query
)

2025-06-03 17:58:50 [127.0.0.1][4][-][info][application] $_GET = [
    'q' => 'Test'
    'limit' => '5'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61585'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/sales/search?q=Test&limit=5'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/sales/search'
    'PHP_SELF' => '/index.php/api/sales/search'
    'QUERY_STRING' => 'q=Test&limit=5'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'REQUEST_TIME_FLOAT' => 1748973530.3836
    'REQUEST_TIME' => 1748973530
]
2025-06-03 17:59:14 [127.0.0.1][4][-][warning][performance] Slow API request: api/auth/login took 836.94ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:59:13 [127.0.0.1][4][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61610'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/auth/login'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/auth/login'
    'PHP_SELF' => '/index.php/api/auth/login'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'CONTENT_LENGTH' => '48'
    'HTTP_CONTENT_LENGTH' => '48'
    'REQUEST_TIME_FLOAT' => 1748973553.2765
    'REQUEST_TIME' => 1748973553
]
2025-06-03 17:59:15 [127.0.0.1][4][-][warning][performance] Slow API request: api/sales/scan-barcode took 200.26ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:59:15 [127.0.0.1][4][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61619'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/sales/scan-barcode'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/sales/scan-barcode'
    'PHP_SELF' => '/index.php/api/sales/scan-barcode'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'CONTENT_LENGTH' => '92'
    'HTTP_CONTENT_LENGTH' => '92'
    'REQUEST_TIME_FLOAT' => 1748973555.321
    'REQUEST_TIME' => 1748973555
]
2025-06-03 17:59:16 [127.0.0.1][4][-][warning][performance] Slow API request: api/receipts/generate took 227.88ms (Memory: 2.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:59:15 [127.0.0.1][4][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61622'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/receipts/generate'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/receipts/generate'
    'PHP_SELF' => '/index.php/api/receipts/generate'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'CONTENT_LENGTH' => '124'
    'HTTP_CONTENT_LENGTH' => '124'
    'REQUEST_TIME_FLOAT' => 1748973555.8401
    'REQUEST_TIME' => 1748973555
]
2025-06-03 17:59:17 [127.0.0.1][4][-][warning][performance] Slow API request: api/receipts/2/print took 221.89ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 17:59:16 [127.0.0.1][4][-][info][application] $_GET = [
    'id' => '2'
]

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61630'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/receipts/2/print'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/receipts/2/print'
    'PHP_SELF' => '/index.php/api/receipts/2/print'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'HTTP_AUTHORIZATION' => '***'
    'CONTENT_LENGTH' => '43'
    'HTTP_CONTENT_LENGTH' => '43'
    'REQUEST_TIME_FLOAT' => 1748973556.9017
    'REQUEST_TIME' => 1748973556
]
2025-06-03 18:05:22 [127.0.0.1][4][-][warning][performance] Slow API request: api/auth/login took 784.46ms (Memory: 2.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-03 18:05:21 [127.0.0.1][4][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '61821'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api/auth/login'
    'REQUEST_METHOD' => 'POST'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api/auth/login'
    'PHP_SELF' => '/index.php/api/auth/login'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'CONTENT_LENGTH' => '48'
    'HTTP_CONTENT_LENGTH' => '48'
    'REQUEST_TIME_FLOAT' => 1748973921.2706
    'REQUEST_TIME' => 1748973921
]
2025-06-04 05:27:19 [127.0.0.1][-][-][warning][performance] Slow API request:  took 418.52ms (Memory: 0.00MB)
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:108
    in D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php:68
    in D:\OSPanel\domains\warehouse\controllers\api\BaseController.php:126
2025-06-04 05:27:17 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = [
    'remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d' => 'eyJpdiI6IjR6U05CdU9OanpvTWgzOEJmYTdKZ0E9PSIsInZhbHVlIjoiMzNNd3RwbURVZ2pCTjRvVDFTbkRBc1FsamozZXZGb3hOVHhiYitFQTRzK1BxTSs1Z0xsZFZsYWZsM0V2UXlxVEc4Q2RpWC9ic0VOK0s3bVk5Vlk2U3ZrME5zdmZ5WFg0b0IvaEs2VUlxTGhjdmZycnBsRXN4OWhMSmxyNnQ4d09yTGxZbDFFdWRpcVhmWVJnV003QkdWZW5qYmNaS0RVVTljdXJsTjBza2QxVld6V09nRE0vdHdNcU8rUEJLOHlJMHVWRDAxWDNTczY0aml3YWxrbE1BZ1NxNFloZEczVzRueEdRNVlNcStDZz0iLCJtYWMiOiI5NTI0Mzg0OTQyYzFmMGQxMjQzZDcxMDk0ZjNiODNiZDQzZmMxNzI5YTYwZDk5MmExNmYyMjhhNjMzNzZmY2M5IiwidGFnIjoiIn0='
    'PHPSESSID' => 'fbom2cc06u4jnhartgfs0hpkgq'
    '_csrf' => '904370a1b85b14d490ae1bdb82fe2d2d1042a8bb82b3f46049820795a811aabfa:2:{i:0;s:5:\"_csrf\";i:1;s:32:\"QK32aOl6wL-pyBiXJ_Lh_M3CFHmw5kNM\";}'
]

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60814'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '9000'
    'REQUEST_URI' => '/'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PHP_SELF' => '/index.php'
    'HTTP_HOST' => 'localhost:9000'
    'HTTP_CONNECTION' => 'keep-alive'
    'HTTP_SEC_CH_UA' => '\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"'
    'HTTP_SEC_CH_UA_MOBILE' => '?0'
    'HTTP_SEC_CH_UA_PLATFORM' => '\"Windows\"'
    'HTTP_UPGRADE_INSECURE_REQUESTS' => '1'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    'HTTP_ACCEPT' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
    'HTTP_SEC_FETCH_SITE' => 'none'
    'HTTP_SEC_FETCH_MODE' => 'navigate'
    'HTTP_SEC_FETCH_USER' => '?1'
    'HTTP_SEC_FETCH_DEST' => 'document'
    'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br, zstd'
    'HTTP_ACCEPT_LANGUAGE' => 'en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4'
    'HTTP_COOKIE' => 'remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjR6U05CdU9OanpvTWgzOEJmYTdKZ0E9PSIsInZhbHVlIjoiMzNNd3RwbURVZ2pCTjRvVDFTbkRBc1FsamozZXZGb3hOVHhiYitFQTRzK1BxTSs1Z0xsZFZsYWZsM0V2UXlxVEc4Q2RpWC9ic0VOK0s3bVk5Vlk2U3ZrME5zdmZ5WFg0b0IvaEs2VUlxTGhjdmZycnBsRXN4OWhMSmxyNnQ4d09yTGxZbDFFdWRpcVhmWVJnV003QkdWZW5qYmNaS0RVVTljdXJsTjBza2QxVld6V09nRE0vdHdNcU8rUEJLOHlJMHVWRDAxWDNTczY0aml3YWxrbE1BZ1NxNFloZEczVzRueEdRNVlNcStDZz0iLCJtYWMiOiI5NTI0Mzg0OTQyYzFmMGQxMjQzZDcxMDk0ZjNiODNiZDQzZmMxNzI5YTYwZDk5MmExNmYyMjhhNjMzNzZmY2M5IiwidGFnIjoiIn0%3D; PHPSESSID=fbom2cc06u4jnhartgfs0hpkgq; _csrf=904370a1b85b14d490ae1bdb82fe2d2d1042a8bb82b3f46049820795a811aabfa%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22QK32aOl6wL-pyBiXJ_Lh_M3CFHmw5kNM%22%3B%7D'
    'REQUEST_TIME_FLOAT' => 1749014837.3011
    'REQUEST_TIME' => 1749014837
]
