// Event Bus for Warehouse Management PWA
export class EventBus {
    constructor() {
        this.events = new Map();
        this.onceEvents = new Map();
        this.maxListeners = 100;
        this.debugMode = false;
    }

    // Subscribe to an event
    on(event, callback, context = null) {
        if (typeof callback !== 'function') {
            throw new Error('Callback must be a function');
        }

        if (!this.events.has(event)) {
            this.events.set(event, []);
        }

        const listeners = this.events.get(event);
        
        // Check max listeners limit
        if (listeners.length >= this.maxListeners) {
            console.warn(`Maximum listeners (${this.maxListeners}) exceeded for event: ${event}`);
        }

        const listener = {
            callback,
            context,
            id: this.generateListenerId()
        };

        listeners.push(listener);

        if (this.debugMode) {
            console.log(`Event listener added: ${event} (${listener.id})`);
        }

        // Return unsubscribe function
        return () => this.off(event, callback, context);
    }

    // Subscribe to an event once
    once(event, callback, context = null) {
        if (typeof callback !== 'function') {
            throw new Error('Callback must be a function');
        }

        if (!this.onceEvents.has(event)) {
            this.onceEvents.set(event, []);
        }

        const listeners = this.onceEvents.get(event);
        
        const listener = {
            callback,
            context,
            id: this.generateListenerId()
        };

        listeners.push(listener);

        if (this.debugMode) {
            console.log(`One-time event listener added: ${event} (${listener.id})`);
        }

        // Return unsubscribe function
        return () => this.offOnce(event, callback, context);
    }

    // Unsubscribe from an event
    off(event, callback = null, context = null) {
        if (!this.events.has(event)) {
            return false;
        }

        const listeners = this.events.get(event);

        if (callback === null) {
            // Remove all listeners for this event
            this.events.delete(event);
            if (this.debugMode) {
                console.log(`All listeners removed for event: ${event}`);
            }
            return true;
        }

        // Remove specific listener
        const index = listeners.findIndex(listener => 
            listener.callback === callback && 
            (context === null || listener.context === context)
        );

        if (index !== -1) {
            const removedListener = listeners.splice(index, 1)[0];
            
            // Remove event if no listeners left
            if (listeners.length === 0) {
                this.events.delete(event);
            }

            if (this.debugMode) {
                console.log(`Event listener removed: ${event} (${removedListener.id})`);
            }
            
            return true;
        }

        return false;
    }

    // Unsubscribe from a once event
    offOnce(event, callback = null, context = null) {
        if (!this.onceEvents.has(event)) {
            return false;
        }

        const listeners = this.onceEvents.get(event);

        if (callback === null) {
            // Remove all once listeners for this event
            this.onceEvents.delete(event);
            if (this.debugMode) {
                console.log(`All one-time listeners removed for event: ${event}`);
            }
            return true;
        }

        // Remove specific once listener
        const index = listeners.findIndex(listener => 
            listener.callback === callback && 
            (context === null || listener.context === context)
        );

        if (index !== -1) {
            const removedListener = listeners.splice(index, 1)[0];
            
            // Remove event if no listeners left
            if (listeners.length === 0) {
                this.onceEvents.delete(event);
            }

            if (this.debugMode) {
                console.log(`One-time event listener removed: ${event} (${removedListener.id})`);
            }
            
            return true;
        }

        return false;
    }

    // Emit an event
    emit(event, ...args) {
        let listenersCount = 0;

        if (this.debugMode) {
            console.log(`Emitting event: ${event}`, args);
        }

        // Execute regular listeners
        if (this.events.has(event)) {
            const listeners = this.events.get(event).slice(); // Create copy to avoid issues with modifications during iteration
            
            listeners.forEach(listener => {
                try {
                    if (listener.context) {
                        listener.callback.apply(listener.context, args);
                    } else {
                        listener.callback(...args);
                    }
                    listenersCount++;
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }

        // Execute once listeners
        if (this.onceEvents.has(event)) {
            const listeners = this.onceEvents.get(event).slice(); // Create copy
            
            listeners.forEach(listener => {
                try {
                    if (listener.context) {
                        listener.callback.apply(listener.context, args);
                    } else {
                        listener.callback(...args);
                    }
                    listenersCount++;
                } catch (error) {
                    console.error(`Error in one-time event listener for ${event}:`, error);
                }
            });

            // Remove all once listeners after execution
            this.onceEvents.delete(event);
        }

        if (this.debugMode) {
            console.log(`Event ${event} executed ${listenersCount} listeners`);
        }

        return listenersCount;
    }

    // Emit event asynchronously
    async emitAsync(event, ...args) {
        let listenersCount = 0;

        if (this.debugMode) {
            console.log(`Emitting async event: ${event}`, args);
        }

        // Execute regular listeners
        if (this.events.has(event)) {
            const listeners = this.events.get(event).slice();
            
            for (const listener of listeners) {
                try {
                    let result;
                    if (listener.context) {
                        result = listener.callback.apply(listener.context, args);
                    } else {
                        result = listener.callback(...args);
                    }
                    
                    // Wait for promise if callback is async
                    if (result instanceof Promise) {
                        await result;
                    }
                    
                    listenersCount++;
                } catch (error) {
                    console.error(`Error in async event listener for ${event}:`, error);
                }
            }
        }

        // Execute once listeners
        if (this.onceEvents.has(event)) {
            const listeners = this.onceEvents.get(event).slice();
            
            for (const listener of listeners) {
                try {
                    let result;
                    if (listener.context) {
                        result = listener.callback.apply(listener.context, args);
                    } else {
                        result = listener.callback(...args);
                    }
                    
                    // Wait for promise if callback is async
                    if (result instanceof Promise) {
                        await result;
                    }
                    
                    listenersCount++;
                } catch (error) {
                    console.error(`Error in async one-time event listener for ${event}:`, error);
                }
            }

            // Remove all once listeners after execution
            this.onceEvents.delete(event);
        }

        if (this.debugMode) {
            console.log(`Async event ${event} executed ${listenersCount} listeners`);
        }

        return listenersCount;
    }

    // Check if event has listeners
    hasListeners(event) {
        return (this.events.has(event) && this.events.get(event).length > 0) ||
               (this.onceEvents.has(event) && this.onceEvents.get(event).length > 0);
    }

    // Get listener count for an event
    getListenerCount(event) {
        let count = 0;
        
        if (this.events.has(event)) {
            count += this.events.get(event).length;
        }
        
        if (this.onceEvents.has(event)) {
            count += this.onceEvents.get(event).length;
        }
        
        return count;
    }

    // Get all events with listeners
    getEvents() {
        const events = new Set();
        
        for (const event of this.events.keys()) {
            events.add(event);
        }
        
        for (const event of this.onceEvents.keys()) {
            events.add(event);
        }
        
        return Array.from(events);
    }

    // Remove all listeners
    removeAllListeners() {
        const eventCount = this.events.size + this.onceEvents.size;
        
        this.events.clear();
        this.onceEvents.clear();
        
        if (this.debugMode) {
            console.log(`Removed all listeners from ${eventCount} events`);
        }
        
        return eventCount;
    }

    // Set max listeners limit
    setMaxListeners(max) {
        if (typeof max !== 'number' || max < 0) {
            throw new Error('Max listeners must be a non-negative number');
        }
        
        this.maxListeners = max;
        
        if (this.debugMode) {
            console.log(`Max listeners set to: ${max}`);
        }
    }

    // Enable/disable debug mode
    setDebugMode(enabled) {
        this.debugMode = !!enabled;
        console.log(`EventBus debug mode: ${this.debugMode ? 'enabled' : 'disabled'}`);
    }

    // Generate unique listener ID
    generateListenerId() {
        return `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Get debug information
    getDebugInfo() {
        const info = {
            totalEvents: this.events.size + this.onceEvents.size,
            regularEvents: this.events.size,
            onceEvents: this.onceEvents.size,
            totalListeners: 0,
            maxListeners: this.maxListeners,
            debugMode: this.debugMode,
            events: {}
        };

        // Count listeners for each event
        for (const [event, listeners] of this.events) {
            info.events[event] = {
                type: 'regular',
                listenerCount: listeners.length
            };
            info.totalListeners += listeners.length;
        }

        for (const [event, listeners] of this.onceEvents) {
            if (info.events[event]) {
                info.events[event].onceListenerCount = listeners.length;
            } else {
                info.events[event] = {
                    type: 'once',
                    listenerCount: listeners.length
                };
            }
            info.totalListeners += listeners.length;
        }

        return info;
    }
}

// Create global event bus instance
const globalEventBus = new EventBus();

export default globalEventBus;
export { EventBus };
