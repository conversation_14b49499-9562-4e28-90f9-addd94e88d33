#!/usr/bin/env node

/**
 * Icon Generator for Warehouse Management PWA
 * Generates all required PWA icons from a base SVG or PNG
 */

const fs = require('fs');
const path = require('path');

// Icon sizes for PWA
const ICON_SIZES = [
    { size: 16, name: 'favicon-16x16.png' },
    { size: 32, name: 'favicon-32x32.png' },
    { size: 72, name: 'icon-72x72.png' },
    { size: 96, name: 'icon-96x96.png' },
    { size: 128, name: 'icon-128x128.png' },
    { size: 144, name: 'icon-144x144.png' },
    { size: 152, name: 'icon-152x152.png' },
    { size: 180, name: 'apple-touch-icon.png' },
    { size: 192, name: 'icon-192x192.png' },
    { size: 384, name: 'icon-384x384.png' },
    { size: 512, name: 'icon-512x512.png' }
];

// Shortcut icons
const SHORTCUT_ICONS = [
    { name: 'shortcut-sale.png', size: 96 },
    { name: 'shortcut-income.png', size: 96 },
    { name: 'shortcut-scan.png', size: 96 },
    { name: 'shortcut-reports.png', size: 96 }
];

// Notification icons
const NOTIFICATION_ICONS = [
    { name: 'notification-success.png', size: 96 },
    { name: 'notification-error.png', size: 96 },
    { name: 'notification-warning.png', size: 96 },
    { name: 'notification-info.png', size: 96 }
];

// Badge icon
const BADGE_ICON = { name: 'badge-72x72.png', size: 72 };

const iconsDir = path.join(__dirname, '../public/icons');

// Create icons directory if it doesn't exist
if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true });
}

/**
 * Generate SVG icon content
 */
function generateSVGIcon(size, type = 'main') {
    const colors = {
        main: '#1f2937',
        sale: '#10b981',
        income: '#3b82f6',
        scan: '#8b5cf6',
        reports: '#f59e0b',
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6'
    };

    const icons = {
        main: `
            <rect x="4" y="4" width="16" height="16" rx="2" fill="currentColor"/>
            <path d="M8 10h8M8 14h6" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
        `,
        sale: `
            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9ZM12 8C14.2 8 16 9.8 16 12S14.2 16 12 16S8 14.2 8 12S9.8 8 12 8Z" fill="currentColor"/>
        `,
        income: `
            <path d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
        `,
        scan: `
            <path d="M3 7V5a2 2 0 012-2h2M3 17v2a2 2 0 002 2h2M21 17v2a2 2 0 01-2 2h-2M21 7V5a2 2 0 00-2-2h-2M7 12h10" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
        `,
        reports: `
            <path d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
        `,
        success: `
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
        `,
        error: `
            <path d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
        `,
        warning: `
            <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
        `,
        info: `
            <path d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
        `
    };

    const color = colors[type] || colors.main;
    const iconPath = icons[type] || icons.main;

    return `
        <svg width="${size}" height="${size}" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <rect width="24" height="24" rx="4" fill="${color}"/>
            <g color="white">
                ${iconPath}
            </g>
        </svg>
    `;
}

/**
 * Generate PNG from SVG using Canvas (Node.js compatible)
 */
function generatePNGFromSVG(svgContent, size, outputPath) {
    // For Node.js environment, we'll create a simple placeholder
    // In a real implementation, you'd use a library like sharp or puppeteer
    
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // Simple placeholder implementation
    // Background
    ctx.fillStyle = '#1f2937';
    ctx.fillRect(0, 0, size, size);
    
    // Icon placeholder
    ctx.fillStyle = '#ffffff';
    ctx.font = `${size * 0.4}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('📦', size / 2, size / 2);
    
    // Save as PNG
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync(outputPath, buffer);
}

/**
 * Create a simple canvas implementation for Node.js
 */
function createCanvas(width, height) {
    // Simplified canvas implementation
    return {
        width,
        height,
        getContext: () => ({
            fillStyle: '',
            font: '',
            textAlign: '',
            textBaseline: '',
            fillRect: () => {},
            fillText: () => {}
        }),
        toBuffer: () => Buffer.alloc(0) // Placeholder
    };
}

/**
 * Generate simple PNG icon (fallback method)
 */
function generateSimplePNG(size, outputPath, type = 'main') {
    // Create a simple colored square as fallback
    const colors = {
        main: '#1f2937',
        sale: '#10b981',
        income: '#3b82f6',
        scan: '#8b5cf6',
        reports: '#f59e0b',
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6'
    };
    
    const color = colors[type] || colors.main;
    
    // Generate SVG and save as file (browsers can render SVG as PNG)
    const svgContent = generateSVGIcon(size, type);
    const svgPath = outputPath.replace('.png', '.svg');
    
    fs.writeFileSync(svgPath, svgContent);
    
    // Create a simple HTML file that can be used to generate PNG
    const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { margin: 0; padding: 20px; background: #f0f0f0; }
                .icon { display: inline-block; margin: 10px; }
            </style>
        </head>
        <body>
            <div class="icon">${svgContent}</div>
            <p>Right-click and save as PNG: ${path.basename(outputPath)}</p>
        </body>
        </html>
    `;
    
    const htmlPath = outputPath.replace('.png', '.html');
    fs.writeFileSync(htmlPath, htmlContent);
    
    console.log(`Generated SVG: ${svgPath}`);
    console.log(`Generated HTML preview: ${htmlPath}`);
}

/**
 * Main function to generate all icons
 */
function generateAllIcons() {
    console.log('Generating PWA icons...');
    
    // Generate main app icons
    ICON_SIZES.forEach(({ size, name }) => {
        const outputPath = path.join(iconsDir, name);
        generateSimplePNG(size, outputPath, 'main');
        console.log(`Generated: ${name} (${size}x${size})`);
    });
    
    // Generate shortcut icons
    SHORTCUT_ICONS.forEach(({ name, size }) => {
        const outputPath = path.join(iconsDir, name);
        const type = name.includes('sale') ? 'sale' :
                    name.includes('income') ? 'income' :
                    name.includes('scan') ? 'scan' : 'reports';
        generateSimplePNG(size, outputPath, type);
        console.log(`Generated: ${name} (${size}x${size})`);
    });
    
    // Generate notification icons
    NOTIFICATION_ICONS.forEach(({ name, size }) => {
        const outputPath = path.join(iconsDir, name);
        const type = name.includes('success') ? 'success' :
                    name.includes('error') ? 'error' :
                    name.includes('warning') ? 'warning' : 'info';
        generateSimplePNG(size, outputPath, type);
        console.log(`Generated: ${name} (${size}x${size})`);
    });
    
    // Generate badge icon
    const badgeOutputPath = path.join(iconsDir, BADGE_ICON.name);
    generateSimplePNG(BADGE_ICON.size, badgeOutputPath, 'main');
    console.log(`Generated: ${BADGE_ICON.name} (${BADGE_ICON.size}x${BADGE_ICON.size})`);
    
    // Generate favicon.ico placeholder
    const faviconPath = path.join(iconsDir, '../favicon.ico');
    fs.writeFileSync(faviconPath, ''); // Placeholder
    console.log('Generated: favicon.ico (placeholder)');
    
    console.log('\n✅ All icons generated successfully!');
    console.log('\n📝 Note: SVG and HTML files are generated for manual PNG conversion.');
    console.log('   Open the HTML files in a browser and save the icons as PNG manually,');
    console.log('   or use a tool like sharp or puppeteer for automated conversion.');
}

// Run the generator
if (require.main === module) {
    generateAllIcons();
}

module.exports = {
    generateAllIcons,
    generateSVGIcon,
    ICON_SIZES,
    SHORTCUT_ICONS,
    NOTIFICATION_ICONS
};
