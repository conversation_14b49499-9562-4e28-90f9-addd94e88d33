// API Configuration for Warehouse Management System

// Environment detection
const isDevelopment = window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1' ||
    window.location.hostname.includes('dev');

const isProduction = !isDevelopment;

// API Configuration
export const API_CONFIG = {
    // Base URL for API requests
    baseURL: isDevelopment
        ? 'http://localhost:8080/api'       // Development URL (PHP built-in server)
        : '/api',                           // Production URL (relative)

    // Request timeout in milliseconds
    timeout: 10000,

    // Number of retry attempts for failed requests
    retries: 3,

    // Cache settings
    cache: {
        enabled: true,
        defaultTTL: 5 * 60 * 1000, // 5 minutes
        maxSize: 50 * 1024 * 1024,  // 50MB
    },

    // Offline settings
    offline: {
        enabled: true,
        queueSize: 100,
        syncInterval: 30000, // 30 seconds
    },

    // Authentication settings
    auth: {
        tokenKey: 'auth_token',
        refreshThreshold: 5 * 60 * 1000, // Refresh token 5 minutes before expiry
        autoRefresh: true,
    },

    // Request headers
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
    },

    // API endpoints mapping
    endpoints: {
        // Authentication
        auth: {
            login: '/auth/login',
            verify: '/auth/verify',
            refresh: '/auth/refresh',
        },

        // Products
        products: {
            list: '/products',
            create: '/products',
            get: '/products/{id}',
            update: '/products/{id}',
            delete: '/products/{id}',
            generateBarcode: '/products/{id}/generate-barcode',
            regenerateBarcode: '/products/{id}/regenerate-barcode',
        },

        // Categories
        categories: {
            list: '/categories',
            create: '/categories',
            get: '/categories/{id}',
            update: '/categories/{id}',
            delete: '/categories/{id}',
        },

        // Sales
        sales: {
            list: '/sales',
            create: '/sales',
            get: '/sales/{id}',
            delete: '/sales/{id}',
        },

        // Income
        income: {
            list: '/incomes',
            create: '/incomes',
            get: '/incomes/{id}',
            delete: '/incomes/{id}',
        },

        // Quick Operations
        quickIncome: {
            scanBarcode: '/income/scan-barcode',
            quickEntry: '/income/quick-entry',
            batch: '/income/batch',
            search: '/income/search',
        },

        quickSales: {
            scanBarcode: '/sales/scan-barcode',
            quickEntry: '/sales/quick-entry',
            search: '/sales/search',
            popular: '/sales/popular',
        },

        // Receipts
        receipts: {
            generate: '/receipts/generate',
            get: '/receipts/{id}',
            print: '/receipts/{id}/print',
            duplicate: '/receipts/{id}/duplicate',
            history: '/receipts/history',
            statistics: '/receipts/statistics',
            byNumber: '/receipts/by-number/{number}',
        },

        // Reports
        reports: {
            stock: '/reports/stock',
            sales: '/reports/sales',
            income: '/reports/income',
            summary: '/reports/summary',
        },
    },

    // Error messages
    errorMessages: {
        network: 'Ошибка сети. Проверьте подключение к интернету.',
        timeout: 'Превышено время ожидания запроса.',
        unauthorized: 'Необходима авторизация.',
        forbidden: 'Недостаточно прав доступа.',
        notFound: 'Запрашиваемый ресурс не найден.',
        serverError: 'Внутренняя ошибка сервера.',
        badRequest: 'Некорректный запрос.',
        conflict: 'Конфликт данных.',
        unprocessableEntity: 'Ошибка валидации данных.',
        tooManyRequests: 'Слишком много запросов. Попробуйте позже.',
        offline: 'Нет соединения с интернетом.',
        unknown: 'Неизвестная ошибка.',
    },

    // HTTP status codes mapping
    statusCodes: {
        200: 'OK',
        201: 'Created',
        202: 'Accepted',
        204: 'No Content',
        400: 'Bad Request',
        401: 'Unauthorized',
        403: 'Forbidden',
        404: 'Not Found',
        409: 'Conflict',
        422: 'Unprocessable Entity',
        429: 'Too Many Requests',
        500: 'Internal Server Error',
        502: 'Bad Gateway',
        503: 'Service Unavailable',
        504: 'Gateway Timeout',
    },

    // Request/Response interceptors
    interceptors: {
        request: [],
        response: [],
    },

    // Development settings
    development: {
        enableLogging: true,
        enableMocking: false,
        mockDelay: 1000,
        enableDebugHeaders: true,
    },

    // Production settings
    production: {
        enableLogging: false,
        enableMocking: false,
        enableDebugHeaders: false,
        enableCompression: true,
    },
};

// Environment-specific overrides
if (isDevelopment) {
    Object.assign(API_CONFIG, API_CONFIG.development);
} else {
    Object.assign(API_CONFIG, API_CONFIG.production);
}

// Helper functions
export const ApiHelpers = {
    // Build endpoint URL with parameters
    buildEndpoint(endpoint, params = {}) {
        let url = endpoint;

        // Replace path parameters
        Object.keys(params).forEach(key => {
            url = url.replace(`{${key}}`, params[key]);
        });

        return url;
    },

    // Get error message by status code
    getErrorMessage(statusCode) {
        const messages = API_CONFIG.errorMessages;

        switch (statusCode) {
            case 400: return messages.badRequest;
            case 401: return messages.unauthorized;
            case 403: return messages.forbidden;
            case 404: return messages.notFound;
            case 409: return messages.conflict;
            case 422: return messages.unprocessableEntity;
            case 429: return messages.tooManyRequests;
            case 500: return messages.serverError;
            case 502: return messages.serverError;
            case 503: return messages.serverError;
            case 504: return messages.timeout;
            default: return messages.unknown;
        }
    },

    // Check if request should be cached
    shouldCache(method, endpoint) {
        if (!API_CONFIG.cache.enabled) return false;
        if (method.toUpperCase() !== 'GET') return false;

        // Cache GET requests for read-only endpoints
        const cacheableEndpoints = [
            '/products',
            '/categories',
            '/reports',
            '/receipts/history',
            '/receipts/statistics',
        ];

        return cacheableEndpoints.some(path => endpoint.includes(path));
    },

    // Check if request should be queued when offline
    shouldQueue(method, endpoint) {
        if (!API_CONFIG.offline.enabled) return false;

        // Queue mutation operations
        const queueableMethods = ['POST', 'PUT', 'PATCH', 'DELETE'];
        return queueableMethods.includes(method.toUpperCase());
    },

    // Validate API response
    validateResponse(response) {
        if (!response || typeof response !== 'object') {
            return false;
        }

        // Check for standard API response format
        return 'success' in response && 'data' in response && 'error' in response;
    },

    // Format API error for display
    formatError(error) {
        if (typeof error === 'string') {
            return error;
        }

        if (error && error.message) {
            return error.message;
        }

        if (error && error.details && typeof error.details === 'object') {
            // Format validation errors
            const details = error.details;
            if (Array.isArray(details)) {
                return details.join(', ');
            }

            if (typeof details === 'object') {
                return Object.values(details).flat().join(', ');
            }
        }

        return API_CONFIG.errorMessages.unknown;
    },
};

// Export configuration
export default API_CONFIG;
