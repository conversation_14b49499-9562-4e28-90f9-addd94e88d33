# Warehouse Management PWA Frontend

Современное Progressive Web Application для системы управления складом с поддержкой офлайн режима и мобильной оптимизацией.

## 🚀 Особенности

- **PWA**: Полная поддержка Progressive Web App с офлайн функциональностью
- **Mobile-First**: Адаптивный дизайн с приоритетом мобильных устройств
- **Barcode Scanning**: Сканирование штрих-кодов через камеру устройства
- **Offline Sync**: Работа в офлайн режиме с синхронизацией при восстановлении соединения
- **Real-time**: Быстрые операции продаж и поступлений
- **Modern UI**: Современный интерфейс с Tailwind CSS
- **33 API эндпоинта**: Полная интеграция с backend системой
- **JWT аутентификация**: Безопасная авторизация пользователей
- **IndexedDB кэширование**: Локальное хранение для офлайн работы

## 🛠 Технический стек

- **HTML5**: Семантическая разметка
- **CSS3**: Современные возможности CSS + Flexbox/Grid
- **Tailwind CSS**: Utility-first CSS фреймворк для быстрой разработки
- **Vanilla JavaScript ES6+**: Модульная архитектура без фреймворков
- **Service Worker**: PWA функциональность и кэширование
- **IndexedDB**: Локальная база данных для офлайн режима
- **Camera API**: Сканирование штрих-кодов
- **Web App Manifest**: Установка как нативное приложение
- **Fetch API**: HTTP клиент с retry логикой

## 📁 Архитектура проекта

```
frontend/
├── public/                    # Статические файлы для production
│   ├── index.html            # Главная HTML страница
│   ├── manifest.json         # PWA манифест
│   ├── sw.js                # Service Worker
│   └── icons/               # PWA иконки (автогенерация)
├── src/                      # Исходный код приложения
│   ├── js/                  # JavaScript модули
│   │   ├── api/             # API интеграция
│   │   │   └── client.js    # HTTP клиент с retry/cache
│   │   ├── components/      # Переиспользуемые компоненты
│   │   │   └── basePage.js  # Базовый класс страниц
│   │   ├── pages/           # Страницы приложения
│   │   │   ├── dashboard.js # Главная панель
│   │   │   ├── products.js  # Управление товарами
│   │   │   ├── quickSale.js # Быстрые продажи
│   │   │   └── ...          # Другие страницы
│   │   ├── services/        # Бизнес-логика сервисов
│   │   │   ├── auth.js      # Аутентификация
│   │   │   ├── cache.js     # Кэширование данных
│   │   │   ├── sync.js      # Офлайн синхронизация
│   │   │   └── notification.js # Уведомления
│   │   └── utils/           # Утилиты и хелперы
│   │       ├── router.js    # SPA роутинг
│   │       ├── eventBus.js  # Система событий
│   │       └── pwaInstaller.js # PWA установка
│   ├── css/                 # Стили
│   │   └── main.css         # Основные стили + Tailwind
│   ├── config/              # Конфигурация
│   │   └── api.js           # Настройки API
│   └── assets/              # Ресурсы (изображения, иконки)
├── docs/                    # Документация
│   ├── api-integration.md   # Руководство по API
│   ├── deployment.md        # Развертывание
│   ├── pwa-setup.md        # Настройка PWA
│   └── development.md       # Разработка
├── scripts/                 # Скрипты сборки
│   ├── generate-icons.js    # Генерация PWA иконок
│   ├── build-manifest.js    # Сборка манифеста
│   └── deploy.js           # Развертывание
├── tests/                   # Тесты
│   ├── unit/               # Модульные тесты
│   ├── e2e/                # End-to-end тесты
│   └── pwa/                # PWA тесты
└── package.json            # Зависимости и скрипты
```

## 🔧 Быстрый старт

### Требования
- **Node.js**: 16.0.0+ (для скриптов сборки)
- **Современный браузер**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **HTTPS**: Обязательно для PWA функций в production
- **Backend API**: Warehouse Management API должен быть запущен

### Установка и запуск

```bash
# 1. Клонирование репозитория
git clone <repository-url>
cd warehouse-management-pwa/frontend

# 2. Установка зависимостей (опционально, для скриптов сборки)
npm install

# 3. Генерация PWA иконок
npm run build:icons

# 4. Настройка API endpoint
# Отредактируйте src/config/api.js
# Укажите правильный URL вашего backend API

# 5. Запуск локального сервера
npm run dev
# или альтернативно:
python -m http.server 8080 --directory public
# или:
php -S localhost:8080 -t public
```

Приложение будет доступно по адресу: `http://localhost:8080`

### Первый запуск
1. Откройте `http://localhost:8080` в браузере
2. Войдите используя учетные данные от backend API
3. Разрешите уведомления (для PWA функций)
4. Для тестирования PWA - используйте HTTPS (например, через ngrok)

## 🔗 Интеграция с Backend API

### Конфигурация подключения
```javascript
// src/config/api.js
export const API_CONFIG = {
    baseURL: 'http://localhost/warehouse/api', // Замените на ваш API URL
    timeout: 10000,
    retries: 3,
    auth: {
        tokenKey: 'auth_token',
        autoRefresh: true
    }
};
```

### Поддерживаемые API эндпоинты (33 эндпоинта)

#### 🔐 Аутентификация (2)
- `POST /api/auth/login` - Вход в систему
- `POST /api/auth/verify` - Проверка токена

#### 📦 Товары (7)
- `GET /api/products` - Список товаров с фильтрацией
- `POST /api/products` - Создание товара
- `GET /api/products/{id}` - Получение товара
- `PUT /api/products/{id}` - Обновление товара
- `DELETE /api/products/{id}` - Удаление товара
- `POST /api/products/{id}/generate-barcode` - Генерация штрих-кода
- `POST /api/products/{id}/regenerate-barcode` - Перегенерация штрих-кода

#### 🏷️ Категории (5)
- `GET /api/categories` - Список категорий
- `POST /api/categories` - Создание категории
- `GET /api/categories/{id}` - Получение категории
- `PUT /api/categories/{id}` - Обновление категории
- `DELETE /api/categories/{id}` - Удаление категории

#### 💰 Продажи (4)
- `GET /api/sales` - История продаж
- `POST /api/sales` - Создание продажи
- `GET /api/sales/{id}` - Получение продажи
- `DELETE /api/sales/{id}` - Отмена продажи

#### 📥 Поступления (4)
- `GET /api/incomes` - История поступлений
- `POST /api/incomes` - Создание поступления
- `GET /api/incomes/{id}` - Получение поступления
- `DELETE /api/incomes/{id}` - Отмена поступления

#### ⚡ Быстрые операции (7)
- `POST /api/income/scan-barcode` - Быстрое поступление по штрих-коду
- `POST /api/income/quick-entry` - Быстрое поступление
- `POST /api/income/batch` - Пакетное поступление
- `GET /api/income/search` - Поиск для поступлений
- `POST /api/sales/scan-barcode` - Быстрая продажа по штрих-коду
- `POST /api/sales/quick-entry` - Быстрая продажа
- `GET /api/sales/search` - Поиск для продаж
- `GET /api/sales/popular` - Популярные товары

#### 🧾 Чеки (4)
- `POST /api/receipts/generate` - Генерация чека
- `GET /api/receipts/{id}` - Получение чека
- `POST /api/receipts/{id}/print` - Печать чека
- `GET /api/receipts/history` - История чеков

### Пример использования API
```javascript
import apiClient from './src/js/api/client.js';

// Аутентификация
const loginResult = await apiClient.post('/auth/login', {
    username: 'admin',
    password: 'password'
});

// Получение товаров
const products = await apiClient.get('/products', {
    params: { page: 1, limit: 20, search: 'товар' }
});

// Быстрая продажа по штрих-коду
const saleResult = await apiClient.post('/sales/scan-barcode', {
    barcode: '1234567890123',
    quantity: 2,
    price_per_unit: 25.99
});
```

## 📱 PWA функциональность

### Service Worker возможности
- **Кэширование ресурсов**: Автоматическое кэширование HTML, CSS, JS, изображений
- **API кэширование**: Кэширование GET запросов с TTL
- **Офлайн режим**: Работа без интернета с локальными данными
- **Background Sync**: Синхронизация операций при восстановлении соединения
- **Push уведомления**: Поддержка push-уведомлений (готово к настройке)

### Установка PWA
```javascript
// Автоматическое предложение установки
window.addEventListener('beforeinstallprompt', (e) => {
    // PWA готово к установке
    showInstallPrompt();
});

// Проверка установки
if (window.matchMedia('(display-mode: standalone)').matches) {
    console.log('PWA установлено');
}
```

### Офлайн синхронизация
```javascript
// Операции автоматически добавляются в очередь при отсутствии сети
const result = await apiClient.post('/sales', saleData);

if (result._meta?.queued) {
    showNotification('Операция будет выполнена при восстановлении соединения');
}

// Мониторинг синхронизации
syncService.on('sync:complete', (data) => {
    showNotification(`Синхронизировано ${data.syncedCount} операций`);
});
```

## 🎯 Основные функции приложения

### 📊 Панель управления (Dashboard)
- Сводная статистика по товарообороту, остаткам, продажам
- Графики продаж за период
- Последние операции
- Уведомления о низких остатках
- Быстрые действия (FAB меню)

### 📦 Управление товарами
- Список товаров с поиском и фильтрацией
- Добавление/редактирование товаров
- Генерация штрих-кодов (ручная и автоматическая)
- Управление категориями
- Отслеживание остатков

### ⚡ Быстрые операции
- **Сканирование штрих-кодов**: Через камеру устройства
- **Быстрые продажи**: Одним касанием по штрих-коду
- **Быстрые поступления**: Мгновенное пополнение остатков
- **Пакетная обработка**: До 100 товаров за раз

### 🧾 Чеки и документы
- Генерация чеков из продаж
- Печать на термопринтер или PDF
- История всех чеков
- Дублирование чеков
- Статистика по чекам

### 📈 Отчеты и аналитика
- Отчет по остаткам товаров
- Отчет по продажам за период
- Отчет по поступлениям
- Сводный отчет с ключевыми метриками
- Экспорт в CSV/PDF

## 🔒 Безопасность и аутентификация

### JWT аутентификация
```javascript
// Автоматическое управление токенами
authService.login('username', 'password')
    .then(result => {
        if (result.success) {
            // Токен автоматически сохранен и будет использоваться
            router.navigate('dashboard');
        }
    });

// Автоматическое обновление токенов
authService.scheduleTokenRefresh(); // Обновление за 5 минут до истечения
```

### Безопасность данных
- Токены хранятся в localStorage/sessionStorage
- HTTPS обязателен в production
- Валидация данных на клиенте
- Защита от XSS и CSRF
- Content Security Policy

### Права доступа
```javascript
// Проверка прав пользователя
if (authService.hasPermission('products:write')) {
    showCreateProductButton();
}

// Роли: admin, user
const permissions = authService.getUserPermissions();
```

## 🧪 Тестирование

### Запуск тестов
```bash
# Все тесты
npm test

# Модульные тесты
npm run test:unit

# E2E тесты
npm run test:e2e

# PWA тесты
npm run test:pwa

# Lighthouse аудит
npm run audit:lighthouse
```

### Структура тестов
```
tests/
├── unit/                   # Модульные тесты
│   ├── api.test.js        # Тесты API клиента
│   ├── auth.test.js       # Тесты аутентификации
│   └── cache.test.js      # Тесты кэширования
├── e2e/                   # End-to-end тесты
│   ├── login.spec.js      # Тест входа в систему
│   ├── products.spec.js   # Тест управления товарами
│   └── sales.spec.js      # Тест продаж
└── pwa/                   # PWA тесты
    ├── offline.test.js    # Тест офлайн режима
    ├── install.test.js    # Тест установки PWA
    └── sync.test.js       # Тест синхронизации
```

## 🚀 Развертывание

### Development
```bash
# Локальная разработка
npm run dev

# С hot reload
npm run serve:node
```

### Production сборка
```bash
# Полная сборка для production
npm run build

# Включает:
# - Генерацию иконок
# - Минификацию CSS/JS
# - Оптимизацию изображений
# - Валидацию PWA манифеста
```

### Развертывание на различных платформах

#### GitHub Pages
```bash
npm run deploy:gh-pages
```

#### Netlify
1. Подключите репозиторий
2. Build command: `npm run build`
3. Publish directory: `public`

#### Docker
```dockerfile
FROM nginx:alpine
COPY public/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
```

### Требования к production
- **HTTPS**: Обязательно для PWA функций
- **Веб-сервер**: Apache, Nginx, или CDN
- **Кэширование**: Настройка правильных заголовков
- **Сжатие**: gzip/brotli для статических файлов

## 📚 Документация

### Подробные руководства
- **[API Integration Guide](docs/api-integration.md)**: Полное руководство по интеграции с API
- **[Deployment Guide](docs/deployment.md)**: Развертывание в различных окружениях
- **[PWA Setup Guide](docs/pwa-setup.md)**: Настройка PWA функций
- **[Development Guide](docs/development.md)**: Руководство для разработчиков

### Архитектурные решения
- **Модульная архитектура**: Разделение по функциональности
- **Event-driven**: Система событий для связи компонентов
- **Offline-first**: Приоритет офлайн функциональности
- **Mobile-first**: Адаптивный дизайн с приоритетом мобильных устройств

## 🔧 Настройка и конфигурация

### Переменные окружения
```javascript
// src/config/api.js
const isDevelopment = window.location.hostname === 'localhost';

export const API_CONFIG = {
    baseURL: isDevelopment
        ? 'http://localhost/warehouse/api'
        : 'https://your-api-domain.com/api',
    // ... другие настройки
};
```

### Кастомизация UI
```css
/* src/css/main.css */
:root {
    --primary-color: #1f2937;    /* Основной цвет */
    --secondary-color: #3b82f6;  /* Вторичный цвет */
    --success-color: #10b981;    /* Цвет успеха */
    --warning-color: #f59e0b;    /* Цвет предупреждения */
    --error-color: #ef4444;      /* Цвет ошибки */
}
```

## 🤝 Вклад в разработку

### Процесс разработки
1. **Fork** репозитория
2. Создайте **feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit** изменения: `git commit -m 'Add amazing feature'`
4. **Push** в branch: `git push origin feature/amazing-feature`
5. Создайте **Pull Request**

### Стандарты кода
```bash
# Проверка кода
npm run lint

# Автоисправление
npm run lint:fix

# Форматирование
npm run format
```

### Требования к PR
- Все тесты должны проходить
- Код должен соответствовать ESLint правилам
- Документация должна быть обновлена
- Lighthouse score > 90

## 📞 Поддержка и сообщество

### Получение помощи
1. **Документация**: Проверьте docs/ директорию
2. **Issues**: Создайте issue в GitHub репозитории
3. **Discussions**: Используйте GitHub Discussions для вопросов
4. **Wiki**: Дополнительная информация в Wiki

### Сообщение об ошибках
При создании issue включите:
- Версию браузера
- Шаги для воспроизведения
- Ожидаемое поведение
- Фактическое поведение
- Скриншоты (если применимо)

### Roadmap
- [ ] Поддержка темной темы
- [ ] Интеграция с внешними сканерами штрих-кодов
- [ ] Расширенная аналитика и дашборды
- [ ] Мультиязычность (i18n)
- [ ] Интеграция с принтерами этикеток
- [ ] API для интеграции с внешними системами

## 📄 Лицензия

MIT License - см. [LICENSE](LICENSE) файл для деталей.

## 🙏 Благодарности

- **Tailwind CSS** - за отличный CSS фреймворк
- **Yii2 Framework** - за надежный backend API
- **PWA Community** - за лучшие практики PWA разработки
- **Open Source Community** - за инструменты и библиотеки

---

**Создано с ❤️ для эффективного управления складом**
