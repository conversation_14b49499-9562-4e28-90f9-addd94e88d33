# Руководство по развертыванию Warehouse Management PWA

## Обзор

Данное руководство описывает процесс развертывания Progressive Web Application для системы управления складом в различных окружениях.

## Требования

### Системные требования
- **Node.js**: версия 16.0.0 или выше
- **npm**: версия 8.0.0 или выше
- **Веб-сервер**: Apache, Nginx, или любой статический веб-сервер
- **HTTPS**: обязательно для PWA функций в production

### Браузерная поддержка
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Подготовка к развертыванию

### 1. Клонирование репозитория
```bash
git clone <repository-url>
cd warehouse-management-pwa/frontend
```

### 2. Установка зависимостей
```bash
npm install
```

### 3. Конфигурация API
Отредактируйте файл `src/config/api.js`:

```javascript
export const API_CONFIG = {
    baseURL: 'https://your-api-domain.com/api', // Замените на ваш API URL
    // ... остальные настройки
};
```

### 4. Генерация иконок
```bash
npm run build:icons
```

## Development окружение

### Локальная разработка
```bash
# Запуск локального сервера
npm run dev

# Альтернативно с Node.js
npm run serve:node
```

Приложение будет доступно по адресу: `http://localhost:8080`

### Тестирование PWA функций
Для полного тестирования PWA функций необходим HTTPS:

```bash
# Используйте ngrok для HTTPS туннеля
npx ngrok http 8080
```

## Production развертывание

### 1. Сборка для production
```bash
npm run build
```

Эта команда:
- Генерирует оптимизированные иконки
- Минифицирует CSS и JavaScript
- Оптимизирует изображения
- Создает production манифест

### 2. Статический хостинг

#### GitHub Pages
```bash
# Развертывание на GitHub Pages
npm run deploy:gh-pages
```

#### Netlify
1. Подключите репозиторий к Netlify
2. Установите команду сборки: `npm run build`
3. Установите папку публикации: `public`

#### Vercel
```bash
# Установка Vercel CLI
npm i -g vercel

# Развертывание
vercel --prod
```

### 3. Веб-сервер конфигурация

#### Apache (.htaccess)
Создайте файл `.htaccess` в папке `public`:

```apache
# PWA Support
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle Angular and other front-end routes
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . /index.html [L]
</IfModule>

# HTTPS Redirect
<IfModule mod_rewrite.c>
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# Cache Control
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
</IfModule>

# Service Worker
<Files "sw.js">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires 0
</Files>

# Manifest
<Files "manifest.json">
    Header set Content-Type "application/manifest+json"
</Files>
```

#### Nginx
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL Configuration
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    root /path/to/warehouse-pwa/public;
    index index.html;
    
    # PWA Support
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Service Worker
    location /sw.js {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires 0;
    }
    
    # Manifest
    location /manifest.json {
        add_header Content-Type "application/manifest+json";
    }
    
    # Static Assets Caching
    location ~* \.(css|js|png|jpg|jpeg|gif|svg|ico)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Security Headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
}

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## Docker развертывание

### Dockerfile
```dockerfile
# Build stage
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built app
COPY --from=builder /app/public /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  warehouse-pwa:
    build: .
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    
  # Optional: Add backend API service
  warehouse-api:
    image: your-api-image
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=your-database-url
    restart: unless-stopped
```

## Мониторинг и обслуживание

### Проверка работоспособности
```bash
# Lighthouse аудит
npm run audit:lighthouse

# Проверка PWA
npm run test:pwa

# Проверка безопасности
npm run audit:security
```

### Логирование
Настройте логирование веб-сервера для мониторинга:

```nginx
# Nginx logging
access_log /var/log/nginx/warehouse-pwa.access.log;
error_log /var/log/nginx/warehouse-pwa.error.log;
```

### Обновления
1. Обновите код в репозитории
2. Пересоберите приложение: `npm run build`
3. Разверните обновленные файлы
4. Service Worker автоматически обновит кэш

## Безопасность

### HTTPS
- **Обязательно** используйте HTTPS в production
- Настройте автоматическое перенаправление с HTTP на HTTPS
- Используйте современные SSL/TLS сертификаты

### Content Security Policy (CSP)
Добавьте CSP заголовки:

```html
<meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com;
    style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com;
    img-src 'self' data: https:;
    connect-src 'self' https://your-api-domain.com;
    manifest-src 'self';
">
```

### API безопасность
- Используйте CORS правильно
- Валидируйте JWT токены
- Ограничьте rate limiting

## Производительность

### Оптимизация
- Включите gzip/brotli сжатие
- Настройте правильное кэширование
- Используйте CDN для статических ресурсов
- Оптимизируйте изображения

### Мониторинг производительности
```bash
# Web Vitals мониторинг
lighthouse http://your-domain.com --output=json
```

## Устранение неполадок

### Общие проблемы

1. **Service Worker не обновляется**
   - Проверьте кэширование SW файла
   - Убедитесь что SW файл не кэшируется

2. **PWA не устанавливается**
   - Проверьте HTTPS
   - Валидируйте manifest.json
   - Проверьте иконки

3. **API недоступен**
   - Проверьте CORS настройки
   - Валидируйте API URL в конфигурации

### Логи и отладка
```javascript
// Включите debug режим в development
localStorage.setItem('debug', 'true');
```

## Поддержка

Для получения поддержки:
1. Проверьте документацию
2. Создайте issue в репозитории
3. Обратитесь к команде разработки

---

**Примечание**: Данное руководство предполагает базовые знания веб-разработки и системного администрирования. Для production развертывания рекомендуется консультация с DevOps специалистом.
