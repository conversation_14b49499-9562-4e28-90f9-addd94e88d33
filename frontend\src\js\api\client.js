// API Client for Warehouse Management System
import { API_CONFIG } from '../../config/api.js';

class ApiClient {
    constructor() {
        this.baseURL = API_CONFIG.baseURL;
        this.timeout = API_CONFIG.timeout || 10000;
        this.retries = API_CONFIG.retries || 3;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }

    // Get authentication token
    getAuthToken() {
        return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
    }

    // Set authentication token
    setAuthToken(token, remember = false) {
        if (remember) {
            localStorage.setItem('auth_token', token);
        } else {
            sessionStorage.setItem('auth_token', token);
        }
    }

    // Remove authentication token
    removeAuthToken() {
        localStorage.removeItem('auth_token');
        sessionStorage.removeItem('auth_token');
    }

    // Build request headers
    buildHeaders(customHeaders = {}) {
        const headers = { ...this.defaultHeaders, ...customHeaders };
        
        const token = this.getAuthToken();
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        
        return headers;
    }

    // Build full URL
    buildURL(endpoint) {
        const url = endpoint.startsWith('http') ? endpoint : `${this.baseURL}${endpoint}`;
        return url;
    }

    // Make HTTP request with retry logic
    async request(method, endpoint, options = {}) {
        const {
            data,
            headers = {},
            params = {},
            timeout = this.timeout,
            retries = this.retries,
            cache = false
        } = options;

        const url = this.buildURL(endpoint);
        const requestHeaders = this.buildHeaders(headers);
        
        // Add query parameters
        const urlWithParams = new URL(url);
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== null) {
                urlWithParams.searchParams.append(key, params[key]);
            }
        });

        const requestOptions = {
            method: method.toUpperCase(),
            headers: requestHeaders,
            signal: AbortSignal.timeout(timeout)
        };

        // Add body for non-GET requests
        if (data && method.toUpperCase() !== 'GET') {
            if (data instanceof FormData) {
                // Remove Content-Type header for FormData (browser will set it)
                delete requestOptions.headers['Content-Type'];
                requestOptions.body = data;
            } else {
                requestOptions.body = JSON.stringify(data);
            }
        }

        // Retry logic
        for (let attempt = 0; attempt <= retries; attempt++) {
            try {
                console.log(`API Request [${method.toUpperCase()}] ${urlWithParams.toString()}`);
                
                const response = await fetch(urlWithParams.toString(), requestOptions);
                
                // Handle response
                const result = await this.handleResponse(response);
                
                // Cache successful GET requests if requested
                if (cache && method.toUpperCase() === 'GET' && result.success) {
                    this.cacheResponse(urlWithParams.toString(), result);
                }
                
                return result;
                
            } catch (error) {
                console.error(`API Request failed (attempt ${attempt + 1}):`, error);
                
                // If this is the last attempt, throw the error
                if (attempt === retries) {
                    // Check if we have cached data for GET requests
                    if (method.toUpperCase() === 'GET') {
                        const cachedData = this.getCachedResponse(urlWithParams.toString());
                        if (cachedData) {
                            console.log('Returning cached data due to network error');
                            return {
                                ...cachedData,
                                _meta: { ...cachedData._meta, fromCache: true, offline: true }
                            };
                        }
                    }
                    
                    throw this.createErrorResponse(error);
                }
                
                // Wait before retry (exponential backoff)
                await this.delay(Math.pow(2, attempt) * 1000);
            }
        }
    }

    // Handle HTTP response
    async handleResponse(response) {
        const contentType = response.headers.get('content-type');
        
        let data;
        if (contentType && contentType.includes('application/json')) {
            data = await response.json();
        } else {
            data = await response.text();
        }

        if (!response.ok) {
            throw {
                status: response.status,
                statusText: response.statusText,
                data: data,
                response: response
            };
        }

        return data;
    }

    // Create error response
    createErrorResponse(error) {
        if (error.name === 'AbortError') {
            return {
                success: false,
                error: {
                    message: 'Превышено время ожидания запроса',
                    code: 'TIMEOUT',
                    details: error
                }
            };
        }

        if (!navigator.onLine) {
            return {
                success: false,
                error: {
                    message: 'Нет соединения с интернетом',
                    code: 'OFFLINE',
                    details: error
                }
            };
        }

        if (error.status) {
            return {
                success: false,
                error: {
                    message: error.data?.error?.message || error.statusText || 'Ошибка сервера',
                    code: error.status,
                    details: error.data
                }
            };
        }

        return {
            success: false,
            error: {
                message: error.message || 'Неизвестная ошибка',
                code: 'UNKNOWN',
                details: error
            }
        };
    }

    // Cache response
    cacheResponse(url, data) {
        try {
            const cacheKey = `api_cache_${btoa(url)}`;
            const cacheData = {
                data: data,
                timestamp: Date.now(),
                url: url
            };
            localStorage.setItem(cacheKey, JSON.stringify(cacheData));
        } catch (error) {
            console.warn('Failed to cache response:', error);
        }
    }

    // Get cached response
    getCachedResponse(url) {
        try {
            const cacheKey = `api_cache_${btoa(url)}`;
            const cached = localStorage.getItem(cacheKey);
            
            if (cached) {
                const cacheData = JSON.parse(cached);
                const age = Date.now() - cacheData.timestamp;
                
                // Return cached data if it's less than 5 minutes old
                if (age < 5 * 60 * 1000) {
                    return cacheData.data;
                } else {
                    // Remove expired cache
                    localStorage.removeItem(cacheKey);
                }
            }
        } catch (error) {
            console.warn('Failed to get cached response:', error);
        }
        
        return null;
    }

    // Utility method for delay
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // HTTP method shortcuts
    async get(endpoint, options = {}) {
        return this.request('GET', endpoint, options);
    }

    async post(endpoint, data, options = {}) {
        return this.request('POST', endpoint, { ...options, data });
    }

    async put(endpoint, data, options = {}) {
        return this.request('PUT', endpoint, { ...options, data });
    }

    async patch(endpoint, data, options = {}) {
        return this.request('PATCH', endpoint, { ...options, data });
    }

    async delete(endpoint, options = {}) {
        return this.request('DELETE', endpoint, options);
    }

    // Upload file
    async upload(endpoint, file, additionalData = {}, options = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        // Add additional data
        Object.keys(additionalData).forEach(key => {
            formData.append(key, additionalData[key]);
        });

        return this.request('POST', endpoint, { ...options, data: formData });
    }

    // Download file
    async download(endpoint, filename, options = {}) {
        try {
            const response = await fetch(this.buildURL(endpoint), {
                headers: this.buildHeaders(options.headers),
                signal: AbortSignal.timeout(this.timeout)
            });

            if (!response.ok) {
                throw new Error(`Download failed: ${response.statusText}`);
            }

            const blob = await response.blob();
            
            // Create download link
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename || 'download';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            return { success: true, filename };
            
        } catch (error) {
            console.error('Download failed:', error);
            return this.createErrorResponse(error);
        }
    }

    // Health check
    async healthCheck() {
        try {
            const response = await this.get('/api', { timeout: 5000 });
            return response.success;
        } catch (error) {
            return false;
        }
    }

    // Clear all cached responses
    clearCache() {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
            if (key.startsWith('api_cache_')) {
                localStorage.removeItem(key);
            }
        });
    }
}

// Create singleton instance
const apiClient = new ApiClient();

export default apiClient;
export { ApiClient };
