// Dashboard Page for Warehouse Management PWA
import BasePage from '../components/basePage.js';
import apiClient from '../api/client.js';
import { API_CONFIG, ApiHelpers } from '../../config/api.js';

export class DashboardPage extends BasePage {
    constructor(options) {
        super(options);
        this.dashboardData = null;
        this.refreshInterval = null;
        this.charts = {};
    }

    async loadData() {
        try {
            // Load dashboard summary data
            const summaryResponse = await apiClient.get(
                ApiHelpers.buildEndpoint(API_CONFIG.endpoints.reports.summary),
                { cache: true }
            );

            if (summaryResponse.success) {
                this.dashboardData = summaryResponse.data;
            } else {
                throw new Error(summaryResponse.error?.message || 'Failed to load dashboard data');
            }

        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            throw error;
        }
    }

    async renderContent() {
        if (!this.dashboardData) {
            this.showError('Не удалось загрузить данные панели управления');
            return;
        }

        this.container.innerHTML = `
            <div class="space-y-6">
                <!-- Header -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Панель управления</h1>
                        <p class="text-gray-600 mt-1">Обзор складских операций</p>
                    </div>
                    <div class="mt-4 sm:mt-0 flex space-x-3">
                        <button id="refresh-btn" class="btn btn-outline">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Обновить
                        </button>
                        <button id="export-btn" class="btn btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            Экспорт
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    ${this.renderStatsCards()}
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Быстрые действия</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        ${this.renderQuickActions()}
                    </div>
                </div>

                <!-- Charts and Recent Activity -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Sales Chart -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Продажи за неделю</h3>
                        <div id="sales-chart" class="h-64"></div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Последние операции</h3>
                        <div id="recent-activity" class="space-y-3">
                            ${this.renderRecentActivity()}
                        </div>
                    </div>
                </div>

                <!-- Low Stock Alert -->
                ${this.renderLowStockAlert()}

                <!-- Performance Metrics -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Показатели производительности</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        ${this.renderPerformanceMetrics()}
                    </div>
                </div>
            </div>
        `;

        // Render charts after DOM is ready
        setTimeout(() => {
            this.renderCharts();
        }, 100);
    }

    renderStatsCards() {
        const stats = [
            {
                title: 'Общий товарооборот',
                value: this.formatCurrency(this.dashboardData.totalRevenue || 0),
                change: this.dashboardData.revenueChange || 0,
                icon: '💰',
                color: 'green'
            },
            {
                title: 'Товаров в наличии',
                value: this.formatNumber(this.dashboardData.totalProducts || 0),
                change: this.dashboardData.productsChange || 0,
                icon: '📦',
                color: 'blue'
            },
            {
                title: 'Продаж сегодня',
                value: this.formatNumber(this.dashboardData.todaySales || 0),
                change: this.dashboardData.salesChange || 0,
                icon: '🛒',
                color: 'purple'
            },
            {
                title: 'Низкие остатки',
                value: this.formatNumber(this.dashboardData.lowStockItems || 0),
                change: this.dashboardData.lowStockChange || 0,
                icon: '⚠️',
                color: 'red'
            }
        ];

        return stats.map(stat => `
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">${stat.title}</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">${stat.value}</p>
                    </div>
                    <div class="text-3xl">${stat.icon}</div>
                </div>
                <div class="mt-4 flex items-center">
                    <span class="text-sm font-medium ${stat.change >= 0 ? 'text-green-600' : 'text-red-600'}">
                        ${stat.change >= 0 ? '+' : ''}${stat.change}%
                    </span>
                    <span class="text-sm text-gray-500 ml-2">за последний месяц</span>
                </div>
            </div>
        `).join('');
    }

    renderQuickActions() {
        const actions = [
            {
                title: 'Быстрая продажа',
                icon: '🛒',
                action: 'quick-sale',
                color: 'green'
            },
            {
                title: 'Поступление',
                icon: '📥',
                action: 'quick-income',
                color: 'blue'
            },
            {
                title: 'Сканировать',
                icon: '📱',
                action: 'scan-barcode',
                color: 'purple'
            },
            {
                title: 'Добавить товар',
                icon: '➕',
                action: 'products/create',
                color: 'indigo'
            }
        ];

        return actions.map(action => `
            <button class="quick-action-btn p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors" data-action="${action.action}">
                <div class="text-2xl mb-2">${action.icon}</div>
                <div class="text-sm font-medium text-gray-900">${action.title}</div>
            </button>
        `).join('');
    }

    renderRecentActivity() {
        const activities = this.dashboardData.recentActivity || [];
        
        if (activities.length === 0) {
            return '<p class="text-gray-500 text-center py-4">Нет последних операций</p>';
        }

        return activities.slice(0, 5).map(activity => `
            <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-${activity.type === 'sale' ? 'green' : 'blue'}-100 rounded-full flex items-center justify-center">
                        <span class="text-${activity.type === 'sale' ? 'green' : 'blue'}-600 text-sm">
                            ${activity.type === 'sale' ? '💰' : '📦'}
                        </span>
                    </div>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900">${activity.description}</p>
                    <p class="text-xs text-gray-500">${this.formatDate(activity.timestamp)}</p>
                </div>
                <div class="text-sm font-medium text-gray-900">
                    ${this.formatCurrency(activity.amount)}
                </div>
            </div>
        `).join('');
    }

    renderLowStockAlert() {
        const lowStockItems = this.dashboardData.lowStockItems || 0;
        
        if (lowStockItems === 0) {
            return '';
        }

        return `
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3 flex-1">
                        <h3 class="text-sm font-medium text-yellow-800">
                            Внимание: низкие остатки
                        </h3>
                        <p class="text-sm text-yellow-700 mt-1">
                            У ${lowStockItems} товаров заканчиваются остатки на складе
                        </p>
                    </div>
                    <div class="ml-3">
                        <button class="btn btn-sm bg-yellow-100 text-yellow-800 hover:bg-yellow-200" onclick="window.warehouseApp.router.navigate('reports/stock?low_stock=true')">
                            Просмотреть
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    renderPerformanceMetrics() {
        const metrics = [
            {
                title: 'Средний чек',
                value: this.formatCurrency(this.dashboardData.averageOrderValue || 0),
                description: 'За последние 30 дней'
            },
            {
                title: 'Оборачиваемость',
                value: `${this.dashboardData.inventoryTurnover || 0} дней`,
                description: 'Средний период оборота'
            },
            {
                title: 'Рентабельность',
                value: `${this.dashboardData.profitMargin || 0}%`,
                description: 'Средняя маржа'
            }
        ];

        return metrics.map(metric => `
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">${metric.value}</div>
                <div class="text-sm font-medium text-gray-600 mt-1">${metric.title}</div>
                <div class="text-xs text-gray-500 mt-1">${metric.description}</div>
            </div>
        `).join('');
    }

    renderCharts() {
        // Simple chart rendering (you could integrate Chart.js or similar)
        const salesChart = this.querySelector('#sales-chart');
        if (salesChart && this.dashboardData.salesChart) {
            this.renderSalesChart(salesChart);
        }
    }

    renderSalesChart(container) {
        // Simple bar chart implementation
        const data = this.dashboardData.salesChart || [];
        const maxValue = Math.max(...data.map(d => d.value));
        
        container.innerHTML = `
            <div class="flex items-end justify-between h-full space-x-2">
                ${data.map(item => `
                    <div class="flex-1 flex flex-col items-center">
                        <div class="w-full bg-primary-600 rounded-t" style="height: ${(item.value / maxValue) * 100}%"></div>
                        <div class="text-xs text-gray-600 mt-2">${item.label}</div>
                        <div class="text-xs font-medium text-gray-900">${this.formatCurrency(item.value)}</div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    setupEventListeners() {
        // Refresh button
        const refreshBtn = this.querySelector('#refresh-btn');
        if (refreshBtn) {
            this.addEventListener(refreshBtn, 'click', () => {
                this.refreshData();
            });
        }

        // Export button
        const exportBtn = this.querySelector('#export-btn');
        if (exportBtn) {
            this.addEventListener(exportBtn, 'click', () => {
                this.exportDashboard();
            });
        }

        // Quick action buttons
        const quickActionBtns = this.querySelectorAll('.quick-action-btn');
        quickActionBtns.forEach(btn => {
            this.addEventListener(btn, 'click', () => {
                const action = btn.dataset.action;
                this.navigate(action);
            });
        });
    }

    async refreshData() {
        try {
            await this.loadData();
            await this.renderContent();
            this.setupEventListeners();
            this.showNotification('Данные обновлены', 'success');
        } catch (error) {
            this.showNotification('Ошибка обновления данных', 'error');
        }
    }

    exportDashboard() {
        // Implement dashboard export functionality
        this.showNotification('Экспорт в разработке', 'info');
    }

    onMount() {
        // Start auto-refresh
        this.refreshInterval = setInterval(() => {
            if (!document.hidden) {
                this.refreshData();
            }
        }, 5 * 60 * 1000); // Refresh every 5 minutes
    }

    onDestroy() {
        // Clear refresh interval
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
    }
}

export default DashboardPage;
