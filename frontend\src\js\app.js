// Warehouse Management PWA - Main Application
import { AuthService } from './services/auth.js';
import { CacheService } from './services/cache.js';
import { SyncService } from './services/sync.js';
import { NotificationService } from './services/notification.js';
import { Router } from './utils/router.js';
import { EventBus } from './utils/eventBus.js';
import { PWAInstaller } from './utils/pwaInstaller.js';

class WarehouseApp {
    constructor() {
        this.isInitialized = false;
        this.isOnline = navigator.onLine;
        
        // Initialize services
        this.authService = new AuthService();
        this.cacheService = new CacheService();
        this.syncService = new SyncService();
        this.notificationService = new NotificationService();
        this.router = new Router();
        this.eventBus = new EventBus();
        this.pwaInstaller = new PWAInstaller();
        
        // Bind methods
        this.handleOnlineStatus = this.handleOnlineStatus.bind(this);
        this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
        this.handleServiceWorkerMessage = this.handleServiceWorkerMessage.bind(this);
    }

    async init() {
        try {
            console.log('Initializing Warehouse PWA...');
            
            // Register service worker
            await this.registerServiceWorker();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Initialize services
            await this.initializeServices();
            
            // Setup UI
            this.setupUI();
            
            // Check authentication
            await this.checkAuthentication();
            
            // Initialize router
            this.router.init();
            
            // Setup PWA installer
            this.pwaInstaller.init();
            
            this.isInitialized = true;
            console.log('Warehouse PWA initialized successfully');
            
            // Hide loading screen
            this.hideLoadingScreen();
            
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showError('Ошибка инициализации приложения');
        }
    }

    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('Service Worker registered:', registration);
                
                // Listen for service worker messages
                navigator.serviceWorker.addEventListener('message', this.handleServiceWorkerMessage);
                
                // Handle service worker updates
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            this.showUpdateAvailable();
                        }
                    });
                });
                
            } catch (error) {
                console.error('Service Worker registration failed:', error);
            }
        }
    }

    setupEventListeners() {
        // Online/offline status
        window.addEventListener('online', this.handleOnlineStatus);
        window.addEventListener('offline', this.handleOnlineStatus);
        
        // Page visibility
        document.addEventListener('visibilitychange', this.handleVisibilityChange);
        
        // App events
        this.eventBus.on('auth:login', this.handleLogin.bind(this));
        this.eventBus.on('auth:logout', this.handleLogout.bind(this));
        this.eventBus.on('sync:complete', this.handleSyncComplete.bind(this));
        
        // UI events
        this.setupUIEventListeners();
    }

    setupUIEventListeners() {
        // Mobile menu toggle
        const menuToggle = document.getElementById('menu-toggle');
        const mobileNav = document.getElementById('mobile-nav');
        
        if (menuToggle && mobileNav) {
            menuToggle.addEventListener('click', () => {
                mobileNav.classList.toggle('hidden');
            });
        }
        
        // User menu toggle
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenu = document.getElementById('user-menu');
        
        if (userMenuButton && userMenu) {
            userMenuButton.addEventListener('click', (e) => {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
            
            // Close user menu when clicking outside
            document.addEventListener('click', () => {
                userMenu.classList.add('hidden');
            });
        }
        
        // FAB menu
        const fabMain = document.getElementById('fab-main');
        const fabMenu = document.getElementById('fab-menu');
        
        if (fabMain && fabMenu) {
            fabMain.addEventListener('click', () => {
                fabMenu.classList.toggle('hidden');
            });
            
            // FAB options
            const fabOptions = document.querySelectorAll('.fab-option');
            fabOptions.forEach(option => {
                option.addEventListener('click', (e) => {
                    const action = e.currentTarget.dataset.action;
                    this.handleFabAction(action);
                    fabMenu.classList.add('hidden');
                });
            });
        }
        
        // Logout button
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.authService.logout();
            });
        }
    }

    async initializeServices() {
        // Initialize cache service
        await this.cacheService.init();
        
        // Initialize sync service
        await this.syncService.init();
        
        // Initialize notification service
        await this.notificationService.init();
        
        // Request notification permission
        if ('Notification' in window && Notification.permission === 'default') {
            await Notification.requestPermission();
        }
    }

    setupUI() {
        // Update connection status
        this.updateConnectionStatus();
        
        // Setup navigation
        this.setupNavigation();
        
        // Setup forms
        this.setupForms();
    }

    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const href = link.getAttribute('href');
                if (href && href.startsWith('#')) {
                    this.router.navigate(href.substring(1));
                }
            });
        });
    }

    setupForms() {
        // Login form
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleLoginSubmit(e);
            });
        }
    }

    async checkAuthentication() {
        const isAuthenticated = await this.authService.isAuthenticated();
        
        if (isAuthenticated) {
            this.showApp();
            await this.loadUserData();
        } else {
            this.showLoginScreen();
        }
    }

    async handleLoginSubmit(event) {
        const formData = new FormData(event.target);
        const username = formData.get('username');
        const password = formData.get('password');
        
        const submitButton = document.getElementById('login-submit');
        const errorElement = document.getElementById('login-error');
        
        try {
            // Disable submit button
            submitButton.disabled = true;
            submitButton.textContent = 'Вход...';
            
            // Hide previous errors
            errorElement.classList.add('hidden');
            
            // Attempt login
            const result = await this.authService.login(username, password);
            
            if (result.success) {
                this.eventBus.emit('auth:login', result.data);
            } else {
                throw new Error(result.error.message);
            }
            
        } catch (error) {
            console.error('Login failed:', error);
            this.showLoginError(error.message);
        } finally {
            // Re-enable submit button
            submitButton.disabled = false;
            submitButton.textContent = 'Войти';
        }
    }

    handleLogin(userData) {
        this.showApp();
        this.loadUserData();
        this.notificationService.show('Добро пожаловать!', 'success');
    }

    handleLogout() {
        this.showLoginScreen();
        this.clearUserData();
        this.notificationService.show('Вы вышли из системы', 'info');
    }

    async loadUserData() {
        try {
            const user = await this.authService.getCurrentUser();
            if (user) {
                this.updateUserUI(user);
            }
        } catch (error) {
            console.error('Failed to load user data:', error);
        }
    }

    updateUserUI(user) {
        const userInitials = document.getElementById('user-initials');
        const userName = document.getElementById('user-name');
        const userEmail = document.getElementById('user-email');
        
        if (userInitials && user.username) {
            userInitials.textContent = user.username.charAt(0).toUpperCase();
        }
        
        if (userName) {
            userName.textContent = user.username || 'Пользователь';
        }
        
        if (userEmail) {
            userEmail.textContent = user.email || '';
        }
    }

    clearUserData() {
        const userInitials = document.getElementById('user-initials');
        const userName = document.getElementById('user-name');
        const userEmail = document.getElementById('user-email');
        
        if (userInitials) userInitials.textContent = 'U';
        if (userName) userName.textContent = 'Пользователь';
        if (userEmail) userEmail.textContent = '';
    }

    showApp() {
        document.getElementById('app').classList.remove('hidden');
        document.getElementById('login-screen').classList.add('hidden');
    }

    showLoginScreen() {
        document.getElementById('app').classList.add('hidden');
        document.getElementById('login-screen').classList.remove('hidden');
    }

    showLoginError(message) {
        const errorElement = document.getElementById('login-error');
        const errorMessage = document.getElementById('login-error-message');
        
        if (errorElement && errorMessage) {
            errorMessage.textContent = message;
            errorElement.classList.remove('hidden');
        }
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 300);
        }
    }

    handleOnlineStatus() {
        this.isOnline = navigator.onLine;
        this.updateConnectionStatus();
        
        if (this.isOnline) {
            this.syncService.syncPendingRequests();
            this.notificationService.show('Соединение восстановлено', 'success');
        } else {
            this.notificationService.show('Нет соединения с интернетом', 'warning');
        }
    }

    updateConnectionStatus() {
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
            const dot = statusElement.querySelector('div');
            const text = statusElement.querySelector('span');
            
            if (this.isOnline) {
                dot.className = 'w-2 h-2 bg-green-500 rounded-full mr-2';
                if (text) text.textContent = 'Онлайн';
            } else {
                dot.className = 'w-2 h-2 bg-red-500 rounded-full mr-2';
                if (text) text.textContent = 'Офлайн';
            }
        }
    }

    handleVisibilityChange() {
        if (!document.hidden && this.isOnline) {
            // App became visible and we're online - sync data
            this.syncService.syncPendingRequests();
        }
    }

    handleServiceWorkerMessage(event) {
        const { type, data } = event.data;
        
        switch (type) {
            case 'SYNC_COMPLETE':
                this.handleSyncComplete(data);
                break;
            case 'CACHE_UPDATED':
                this.notificationService.show('Данные обновлены', 'info');
                break;
        }
    }

    handleSyncComplete(data) {
        if (data.syncedCount > 0) {
            this.notificationService.show(
                `Синхронизировано ${data.syncedCount} операций`,
                'success'
            );
        }
    }

    handleFabAction(action) {
        switch (action) {
            case 'quick-sale':
                this.router.navigate('quick-sale');
                break;
            case 'quick-income':
                this.router.navigate('quick-income');
                break;
            case 'scan-barcode':
                this.router.navigate('scan-barcode');
                break;
        }
    }

    showUpdateAvailable() {
        const updateBanner = document.createElement('div');
        updateBanner.className = 'fixed top-0 left-0 right-0 bg-blue-600 text-white p-3 text-center z-50';
        updateBanner.innerHTML = `
            <span>Доступно обновление приложения</span>
            <button id="update-app" class="ml-4 bg-blue-700 px-3 py-1 rounded text-sm">
                Обновить
            </button>
            <button id="dismiss-update" class="ml-2 text-blue-200">×</button>
        `;
        
        document.body.appendChild(updateBanner);
        
        document.getElementById('update-app').addEventListener('click', () => {
            if (navigator.serviceWorker.controller) {
                navigator.serviceWorker.controller.postMessage({ type: 'SKIP_WAITING' });
                window.location.reload();
            }
        });
        
        document.getElementById('dismiss-update').addEventListener('click', () => {
            updateBanner.remove();
        });
    }

    showError(message) {
        this.notificationService.show(message, 'error');
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const app = new WarehouseApp();
    app.init();
    
    // Make app globally available for debugging
    window.warehouseApp = app;
});

// Handle app installation
window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    window.deferredPrompt = e;
});

export default WarehouseApp;
