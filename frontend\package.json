{"name": "warehouse-management-pwa", "version": "1.0.0", "description": "Progressive Web Application for Warehouse Management System", "main": "public/index.html", "scripts": {"dev": "npm run serve", "serve": "python -m http.server 8080 --directory public", "serve:node": "npx http-server public -p 8080 -c-1", "build": "npm run build:icons && npm run build:manifest && npm run optimize", "build:icons": "node scripts/generate-icons.js", "build:manifest": "node scripts/build-manifest.js", "optimize": "npm run optimize:images && npm run optimize:css && npm run optimize:js", "optimize:images": "node scripts/optimize-images.js", "optimize:css": "npm run build:css", "optimize:js": "npm run build:js", "build:css": "tailwindcss -i src/css/main.css -o public/css/main.min.css --minify", "build:js": "node scripts/bundle-js.js", "test": "npm run test:unit && npm run test:e2e", "test:unit": "jest", "test:e2e": "playwright test", "test:pwa": "npm run test:lighthouse && npm run test:workbox", "test:lighthouse": "lighthouse http://localhost:8080 --output=html --output-path=./reports/lighthouse.html", "test:workbox": "workbox-cli --help", "audit": "npm run audit:lighthouse && npm run audit:security", "audit:lighthouse": "lighthouse http://localhost:8080 --output=json --output-path=./reports/lighthouse.json", "audit:security": "npm audit", "deploy": "npm run build && npm run deploy:static", "deploy:static": "node scripts/deploy.js", "deploy:gh-pages": "gh-pages -d public", "clean": "rimraf public/css/main.min.css public/js/bundle.min.js reports/", "lint": "eslint src/js/**/*.js", "lint:fix": "eslint src/js/**/*.js --fix", "format": "prettier --write src/**/*.{js,css,html}", "validate": "npm run lint && npm run test:unit", "precommit": "npm run validate", "start": "npm run serve", "postinstall": "npm run build:icons"}, "keywords": ["pwa", "warehouse", "management", "inventory", "barcode", "offline", "mobile"], "author": "Warehouse Management Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/warehouse-management-pwa.git"}, "bugs": {"url": "https://github.com/your-org/warehouse-management-pwa/issues"}, "homepage": "https://your-org.github.io/warehouse-management-pwa", "devDependencies": {"@playwright/test": "^1.40.0", "eslint": "^8.55.0", "gh-pages": "^6.1.0", "http-server": "^14.1.1", "jest": "^29.7.0", "lighthouse": "^11.4.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "sharp": "^0.33.0", "tailwindcss": "^3.3.6", "workbox-cli": "^7.0.0"}, "dependencies": {}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "pwa": {"name": "Warehouse Management", "shortName": "Warehouse", "description": "PWA for warehouse and inventory management", "themeColor": "#1f2937", "backgroundColor": "#ffffff", "display": "standalone", "orientation": "portrait-primary", "scope": "/", "startUrl": "/"}, "config": {"api": {"development": "http://localhost/warehouse/api", "production": "/api"}, "build": {"outputDir": "dist", "assetsDir": "assets", "publicPath": "/"}}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "testMatch": ["<rootDir>/tests/**/*.test.js"], "collectCoverageFrom": ["src/js/**/*.js", "!src/js/**/*.test.js"], "coverageDirectory": "reports/coverage", "coverageReporters": ["html", "text", "lcov"]}, "eslintConfig": {"env": {"browser": true, "es2021": true, "serviceworker": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"no-console": "warn", "no-unused-vars": "error", "no-undef": "error", "prefer-const": "error", "no-var": "error"}, "globals": {"workbox": "readonly"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false}}