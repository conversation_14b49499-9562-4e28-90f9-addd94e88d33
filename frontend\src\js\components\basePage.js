// Base Page Component for Warehouse Management PWA
export class BasePage {
    constructor(options = {}) {
        this.route = options.route;
        this.params = options.params || {};
        this.router = options.router;
        this.container = null;
        this.isDestroyed = false;
        this.eventListeners = [];
        
        // Bind methods
        this.handleResize = this.handleResize.bind(this);
        this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
    }

    // Render the page
    async render(container) {
        this.container = container;
        
        try {
            // Show loading state
            this.showLoading();
            
            // Load data if needed
            await this.loadData();
            
            // Render content
            await this.renderContent();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Setup global event listeners
            this.setupGlobalEventListeners();
            
            // Call onMount hook
            this.onMount();
            
        } catch (error) {
            console.error('Failed to render page:', error);
            this.showError('Ошибка загрузки страницы');
        }
    }

    // Load data (override in subclasses)
    async loadData() {
        // Override in subclasses
    }

    // Render content (override in subclasses)
    async renderContent() {
        this.container.innerHTML = `
            <div class="text-center py-12">
                <h1 class="text-2xl font-bold text-gray-900">Базовая страница</h1>
                <p class="text-gray-600 mt-2">Эта страница должна быть переопределена в подклассе</p>
            </div>
        `;
    }

    // Setup event listeners (override in subclasses)
    setupEventListeners() {
        // Override in subclasses
    }

    // Setup global event listeners
    setupGlobalEventListeners() {
        window.addEventListener('resize', this.handleResize);
        document.addEventListener('visibilitychange', this.handleVisibilityChange);
    }

    // Handle window resize
    handleResize() {
        if (!this.isDestroyed) {
            this.onResize();
        }
    }

    // Handle visibility change
    handleVisibilityChange() {
        if (!this.isDestroyed) {
            if (document.hidden) {
                this.onHide();
            } else {
                this.onShow();
            }
        }
    }

    // Lifecycle hooks (override in subclasses)
    onMount() {
        // Called after page is mounted
    }

    onResize() {
        // Called when window is resized
    }

    onShow() {
        // Called when page becomes visible
    }

    onHide() {
        // Called when page becomes hidden
    }

    onDestroy() {
        // Called before page is destroyed
    }

    // Show loading state
    showLoading() {
        if (this.container) {
            this.container.innerHTML = `
                <div class="flex items-center justify-center h-64">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
                    <span class="ml-3 text-gray-600">Загрузка...</span>
                </div>
            `;
        }
    }

    // Show error state
    showError(message, details = null) {
        if (this.container) {
            this.container.innerHTML = `
                <div class="text-center py-12">
                    <div class="text-red-500 text-6xl mb-4">⚠️</div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Ошибка</h2>
                    <p class="text-gray-600 mb-4">${message}</p>
                    ${details ? `<details class="text-sm text-gray-500 mb-4"><summary>Подробности</summary><pre class="mt-2 text-left">${details}</pre></details>` : ''}
                    <button onclick="location.reload()" class="btn btn-primary">
                        Обновить страницу
                    </button>
                </div>
            `;
        }
    }

    // Show empty state
    showEmpty(message, actionText = null, actionCallback = null) {
        if (this.container) {
            this.container.innerHTML = `
                <div class="text-center py-12">
                    <div class="text-gray-400 text-6xl mb-4">📭</div>
                    <h2 class="text-xl font-semibold text-gray-900 mb-2">Нет данных</h2>
                    <p class="text-gray-600 mb-4">${message}</p>
                    ${actionText ? `<button id="empty-action-btn" class="btn btn-primary">${actionText}</button>` : ''}
                </div>
            `;
            
            if (actionText && actionCallback) {
                const actionBtn = this.container.querySelector('#empty-action-btn');
                if (actionBtn) {
                    actionBtn.addEventListener('click', actionCallback);
                }
            }
        }
    }

    // Add event listener with cleanup tracking
    addEventListener(element, event, handler, options = {}) {
        element.addEventListener(event, handler, options);
        this.eventListeners.push({ element, event, handler, options });
    }

    // Remove all event listeners
    removeEventListeners() {
        this.eventListeners.forEach(({ element, event, handler, options }) => {
            element.removeEventListener(event, handler, options);
        });
        this.eventListeners = [];
    }

    // Create element with classes
    createElement(tag, classes = '', attributes = {}) {
        const element = document.createElement(tag);
        
        if (classes) {
            element.className = classes;
        }
        
        Object.keys(attributes).forEach(key => {
            element.setAttribute(key, attributes[key]);
        });
        
        return element;
    }

    // Find element in container
    querySelector(selector) {
        return this.container ? this.container.querySelector(selector) : null;
    }

    // Find elements in container
    querySelectorAll(selector) {
        return this.container ? this.container.querySelectorAll(selector) : [];
    }

    // Navigate to another page
    navigate(path) {
        if (this.router) {
            this.router.navigate(path);
        }
    }

    // Get route parameter
    getParam(name, defaultValue = null) {
        return this.params[name] || defaultValue;
    }

    // Get query parameter
    getQuery(name, defaultValue = null) {
        const params = new URLSearchParams(window.location.search);
        return params.get(name) || defaultValue;
    }

    // Set page title
    setTitle(title) {
        document.title = `${title} - Система управления складом`;
    }

    // Show notification
    showNotification(message, type = 'info', options = {}) {
        // This would integrate with the notification service
        console.log(`Notification [${type}]: ${message}`);
    }

    // Format date
    formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        };
        
        return new Intl.DateTimeFormat('ru-RU', { ...defaultOptions, ...options }).format(new Date(date));
    }

    // Format currency
    formatCurrency(amount, currency = 'RUB') {
        return new Intl.NumberFormat('ru-RU', {
            style: 'currency',
            currency: currency
        }).format(amount);
    }

    // Format number
    formatNumber(number, options = {}) {
        return new Intl.NumberFormat('ru-RU', options).format(number);
    }

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Throttle function
    throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Destroy page
    destroy() {
        if (this.isDestroyed) {
            return;
        }
        
        // Call onDestroy hook
        this.onDestroy();
        
        // Remove event listeners
        this.removeEventListeners();
        
        // Remove global event listeners
        window.removeEventListener('resize', this.handleResize);
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        
        // Clear container
        if (this.container) {
            this.container.innerHTML = '';
        }
        
        // Mark as destroyed
        this.isDestroyed = true;
        
        console.log('Page destroyed');
    }
}

export default BasePage;
