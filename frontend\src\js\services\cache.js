// Cache Service for Warehouse Management System
import { API_CONFIG } from '../../config/api.js';

export class CacheService {
    constructor() {
        this.dbName = 'WarehouseCacheDB';
        this.dbVersion = 1;
        this.db = null;
        this.stores = {
            apiCache: 'api_cache',
            offlineQueue: 'offline_queue',
            userPreferences: 'user_preferences',
            appData: 'app_data'
        };
    }

    // Initialize cache service
    async init() {
        try {
            this.db = await this.openDatabase();
            console.log('Cache service initialized successfully');
            
            // Clean expired cache entries
            await this.cleanExpiredEntries();
            
            return true;
        } catch (error) {
            console.error('Failed to initialize cache service:', error);
            return false;
        }
    }

    // Open IndexedDB database
    openDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);
            
            request.onerror = () => {
                reject(new Error('Failed to open database'));
            };
            
            request.onsuccess = () => {
                resolve(request.result);
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create API cache store
                if (!db.objectStoreNames.contains(this.stores.apiCache)) {
                    const apiStore = db.createObjectStore(this.stores.apiCache, { keyPath: 'key' });
                    apiStore.createIndex('url', 'url', { unique: false });
                    apiStore.createIndex('timestamp', 'timestamp', { unique: false });
                    apiStore.createIndex('expiry', 'expiry', { unique: false });
                }
                
                // Create offline queue store
                if (!db.objectStoreNames.contains(this.stores.offlineQueue)) {
                    const queueStore = db.createObjectStore(this.stores.offlineQueue, { 
                        keyPath: 'id', 
                        autoIncrement: true 
                    });
                    queueStore.createIndex('timestamp', 'timestamp', { unique: false });
                    queueStore.createIndex('method', 'method', { unique: false });
                    queueStore.createIndex('priority', 'priority', { unique: false });
                }
                
                // Create user preferences store
                if (!db.objectStoreNames.contains(this.stores.userPreferences)) {
                    db.createObjectStore(this.stores.userPreferences, { keyPath: 'key' });
                }
                
                // Create app data store
                if (!db.objectStoreNames.contains(this.stores.appData)) {
                    const appStore = db.createObjectStore(this.stores.appData, { keyPath: 'key' });
                    appStore.createIndex('category', 'category', { unique: false });
                    appStore.createIndex('timestamp', 'timestamp', { unique: false });
                }
            };
        });
    }

    // Cache API response
    async cacheApiResponse(url, method, data, ttl = null) {
        if (!this.db || !API_CONFIG.cache.enabled) {
            return false;
        }

        try {
            const key = this.generateCacheKey(url, method);
            const timestamp = Date.now();
            const expiry = timestamp + (ttl || API_CONFIG.cache.defaultTTL);
            
            const cacheEntry = {
                key,
                url,
                method: method.toUpperCase(),
                data,
                timestamp,
                expiry,
                size: this.calculateSize(data)
            };

            const transaction = this.db.transaction([this.stores.apiCache], 'readwrite');
            const store = transaction.objectStore(this.stores.apiCache);
            
            await this.promisifyRequest(store.put(cacheEntry));
            
            console.log(`Cached API response: ${method} ${url}`);
            return true;
            
        } catch (error) {
            console.error('Failed to cache API response:', error);
            return false;
        }
    }

    // Get cached API response
    async getCachedApiResponse(url, method) {
        if (!this.db || !API_CONFIG.cache.enabled) {
            return null;
        }

        try {
            const key = this.generateCacheKey(url, method);
            const transaction = this.db.transaction([this.stores.apiCache], 'readonly');
            const store = transaction.objectStore(this.stores.apiCache);
            
            const result = await this.promisifyRequest(store.get(key));
            
            if (result && result.expiry > Date.now()) {
                console.log(`Cache hit: ${method} ${url}`);
                return {
                    ...result.data,
                    _meta: {
                        ...result.data._meta,
                        fromCache: true,
                        cachedAt: result.timestamp
                    }
                };
            } else if (result) {
                // Entry expired, remove it
                await this.removeCachedApiResponse(url, method);
            }
            
            return null;
            
        } catch (error) {
            console.error('Failed to get cached API response:', error);
            return null;
        }
    }

    // Remove cached API response
    async removeCachedApiResponse(url, method) {
        if (!this.db) {
            return false;
        }

        try {
            const key = this.generateCacheKey(url, method);
            const transaction = this.db.transaction([this.stores.apiCache], 'readwrite');
            const store = transaction.objectStore(this.stores.apiCache);
            
            await this.promisifyRequest(store.delete(key));
            return true;
            
        } catch (error) {
            console.error('Failed to remove cached API response:', error);
            return false;
        }
    }

    // Add request to offline queue
    async addToOfflineQueue(request, priority = 1) {
        if (!this.db || !API_CONFIG.offline.enabled) {
            return false;
        }

        try {
            const queueEntry = {
                url: request.url,
                method: request.method,
                headers: request.headers,
                body: request.body,
                timestamp: Date.now(),
                priority,
                retries: 0,
                maxRetries: 3
            };

            const transaction = this.db.transaction([this.stores.offlineQueue], 'readwrite');
            const store = transaction.objectStore(this.stores.offlineQueue);
            
            const result = await this.promisifyRequest(store.add(queueEntry));
            
            console.log(`Added request to offline queue: ${request.method} ${request.url}`);
            return result;
            
        } catch (error) {
            console.error('Failed to add request to offline queue:', error);
            return false;
        }
    }

    // Get offline queue
    async getOfflineQueue() {
        if (!this.db) {
            return [];
        }

        try {
            const transaction = this.db.transaction([this.stores.offlineQueue], 'readonly');
            const store = transaction.objectStore(this.stores.offlineQueue);
            const index = store.index('priority');
            
            const result = await this.promisifyRequest(index.getAll());
            
            // Sort by priority (higher first) and timestamp (older first)
            return result.sort((a, b) => {
                if (a.priority !== b.priority) {
                    return b.priority - a.priority;
                }
                return a.timestamp - b.timestamp;
            });
            
        } catch (error) {
            console.error('Failed to get offline queue:', error);
            return [];
        }
    }

    // Remove request from offline queue
    async removeFromOfflineQueue(id) {
        if (!this.db) {
            return false;
        }

        try {
            const transaction = this.db.transaction([this.stores.offlineQueue], 'readwrite');
            const store = transaction.objectStore(this.stores.offlineQueue);
            
            await this.promisifyRequest(store.delete(id));
            return true;
            
        } catch (error) {
            console.error('Failed to remove request from offline queue:', error);
            return false;
        }
    }

    // Store user preference
    async setUserPreference(key, value) {
        if (!this.db) {
            return false;
        }

        try {
            const transaction = this.db.transaction([this.stores.userPreferences], 'readwrite');
            const store = transaction.objectStore(this.stores.userPreferences);
            
            await this.promisifyRequest(store.put({ key, value, timestamp: Date.now() }));
            return true;
            
        } catch (error) {
            console.error('Failed to set user preference:', error);
            return false;
        }
    }

    // Get user preference
    async getUserPreference(key, defaultValue = null) {
        if (!this.db) {
            return defaultValue;
        }

        try {
            const transaction = this.db.transaction([this.stores.userPreferences], 'readonly');
            const store = transaction.objectStore(this.stores.userPreferences);
            
            const result = await this.promisifyRequest(store.get(key));
            return result ? result.value : defaultValue;
            
        } catch (error) {
            console.error('Failed to get user preference:', error);
            return defaultValue;
        }
    }

    // Store app data
    async setAppData(key, data, category = 'general') {
        if (!this.db) {
            return false;
        }

        try {
            const transaction = this.db.transaction([this.stores.appData], 'readwrite');
            const store = transaction.objectStore(this.stores.appData);
            
            const entry = {
                key,
                data,
                category,
                timestamp: Date.now()
            };
            
            await this.promisifyRequest(store.put(entry));
            return true;
            
        } catch (error) {
            console.error('Failed to set app data:', error);
            return false;
        }
    }

    // Get app data
    async getAppData(key, defaultValue = null) {
        if (!this.db) {
            return defaultValue;
        }

        try {
            const transaction = this.db.transaction([this.stores.appData], 'readonly');
            const store = transaction.objectStore(this.stores.appData);
            
            const result = await this.promisifyRequest(store.get(key));
            return result ? result.data : defaultValue;
            
        } catch (error) {
            console.error('Failed to get app data:', error);
            return defaultValue;
        }
    }

    // Utility methods
    generateCacheKey(url, method) {
        return `${method.toUpperCase()}_${btoa(url).replace(/[^a-zA-Z0-9]/g, '')}`;
    }

    calculateSize(data) {
        return new Blob([JSON.stringify(data)]).size;
    }

    promisifyRequest(request) {
        return new Promise((resolve, reject) => {
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // Clean expired cache entries
    async cleanExpiredEntries() {
        if (!this.db) {
            return;
        }

        try {
            const transaction = this.db.transaction([this.stores.apiCache], 'readwrite');
            const store = transaction.objectStore(this.stores.apiCache);
            const index = store.index('expiry');
            
            const range = IDBKeyRange.upperBound(Date.now());
            const request = index.openCursor(range);
            
            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    cursor.delete();
                    cursor.continue();
                }
            };
            
        } catch (error) {
            console.error('Failed to clean expired cache entries:', error);
        }
    }

    // Clear all cache
    async clearCache() {
        if (!this.db) {
            return false;
        }

        try {
            const transaction = this.db.transaction([this.stores.apiCache], 'readwrite');
            const store = transaction.objectStore(this.stores.apiCache);
            
            await this.promisifyRequest(store.clear());
            console.log('Cache cleared successfully');
            return true;
            
        } catch (error) {
            console.error('Failed to clear cache:', error);
            return false;
        }
    }

    // Get cache statistics
    async getCacheStats() {
        if (!this.db) {
            return null;
        }

        try {
            const transaction = this.db.transaction([this.stores.apiCache], 'readonly');
            const store = transaction.objectStore(this.stores.apiCache);
            
            const count = await this.promisifyRequest(store.count());
            const entries = await this.promisifyRequest(store.getAll());
            
            const totalSize = entries.reduce((sum, entry) => sum + (entry.size || 0), 0);
            const expired = entries.filter(entry => entry.expiry < Date.now()).length;
            
            return {
                totalEntries: count,
                totalSize,
                expiredEntries: expired,
                validEntries: count - expired
            };
            
        } catch (error) {
            console.error('Failed to get cache stats:', error);
            return null;
        }
    }
}

export default CacheService;
