// Sync Service for Warehouse Management System
import apiClient from '../api/client.js';
import { API_CONFIG } from '../../config/api.js';
import { CacheService } from './cache.js';
import { EventBus } from '../utils/eventBus.js';

export class SyncService {
    constructor() {
        this.cacheService = new CacheService();
        this.eventBus = new EventBus();
        this.syncInterval = null;
        this.isSyncing = false;
        this.syncQueue = [];
        this.lastSyncTime = null;
        
        // Bind methods
        this.syncPendingRequests = this.syncPendingRequests.bind(this);
        this.handleOnlineStatus = this.handleOnlineStatus.bind(this);
    }

    // Initialize sync service
    async init() {
        try {
            // Initialize cache service if not already done
            if (!this.cacheService.db) {
                await this.cacheService.init();
            }
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Start periodic sync if online
            if (navigator.onLine) {
                this.startPeriodicSync();
            }
            
            console.log('Sync service initialized successfully');
            return true;
            
        } catch (error) {
            console.error('Failed to initialize sync service:', error);
            return false;
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Listen for online/offline events
        window.addEventListener('online', this.handleOnlineStatus);
        window.addEventListener('offline', this.handleOnlineStatus);
        
        // Listen for visibility change to sync when app becomes visible
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && navigator.onLine) {
                this.syncPendingRequests();
            }
        });
    }

    // Handle online/offline status changes
    handleOnlineStatus() {
        if (navigator.onLine) {
            console.log('Connection restored, starting sync...');
            this.startPeriodicSync();
            this.syncPendingRequests();
        } else {
            console.log('Connection lost, stopping sync...');
            this.stopPeriodicSync();
        }
    }

    // Start periodic sync
    startPeriodicSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }
        
        this.syncInterval = setInterval(() => {
            if (navigator.onLine && !this.isSyncing) {
                this.syncPendingRequests();
            }
        }, API_CONFIG.offline.syncInterval);
        
        console.log(`Periodic sync started (interval: ${API_CONFIG.offline.syncInterval}ms)`);
    }

    // Stop periodic sync
    stopPeriodicSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
        
        console.log('Periodic sync stopped');
    }

    // Add request to sync queue
    async queueRequest(request, priority = 1) {
        if (!API_CONFIG.offline.enabled) {
            return false;
        }

        try {
            // Add to cache service offline queue
            const result = await this.cacheService.addToOfflineQueue(request, priority);
            
            if (result) {
                console.log(`Request queued for sync: ${request.method} ${request.url}`);
                
                // Emit event
                this.eventBus.emit('sync:requestQueued', {
                    request,
                    queueSize: await this.getQueueSize()
                });
                
                // Try immediate sync if online
                if (navigator.onLine) {
                    setTimeout(() => this.syncPendingRequests(), 100);
                }
            }
            
            return result;
            
        } catch (error) {
            console.error('Failed to queue request:', error);
            return false;
        }
    }

    // Sync pending requests
    async syncPendingRequests() {
        if (this.isSyncing || !navigator.onLine) {
            return;
        }

        this.isSyncing = true;
        
        try {
            console.log('Starting sync of pending requests...');
            
            // Get offline queue
            const queue = await this.cacheService.getOfflineQueue();
            
            if (queue.length === 0) {
                console.log('No pending requests to sync');
                return;
            }
            
            console.log(`Syncing ${queue.length} pending requests...`);
            
            let syncedCount = 0;
            let failedCount = 0;
            const results = [];
            
            // Process queue items
            for (const item of queue) {
                try {
                    const result = await this.syncRequest(item);
                    
                    if (result.success) {
                        // Remove from queue
                        await this.cacheService.removeFromOfflineQueue(item.id);
                        syncedCount++;
                        
                        results.push({
                            id: item.id,
                            success: true,
                            data: result.data
                        });
                        
                        console.log(`Synced: ${item.method} ${item.url}`);
                        
                    } else {
                        // Increment retry count
                        item.retries++;
                        
                        if (item.retries >= item.maxRetries) {
                            // Max retries reached, remove from queue
                            await this.cacheService.removeFromOfflineQueue(item.id);
                            failedCount++;
                            
                            console.error(`Max retries reached for: ${item.method} ${item.url}`);
                        }
                        
                        results.push({
                            id: item.id,
                            success: false,
                            error: result.error
                        });
                    }
                    
                } catch (error) {
                    console.error(`Sync failed for: ${item.method} ${item.url}`, error);
                    failedCount++;
                    
                    results.push({
                        id: item.id,
                        success: false,
                        error: error.message
                    });
                }
            }
            
            this.lastSyncTime = Date.now();
            
            // Emit sync complete event
            this.eventBus.emit('sync:complete', {
                syncedCount,
                failedCount,
                totalCount: queue.length,
                results,
                timestamp: this.lastSyncTime
            });
            
            console.log(`Sync completed: ${syncedCount} synced, ${failedCount} failed`);
            
        } catch (error) {
            console.error('Sync process failed:', error);
            
            this.eventBus.emit('sync:error', {
                error: error.message,
                timestamp: Date.now()
            });
            
        } finally {
            this.isSyncing = false;
        }
    }

    // Sync individual request
    async syncRequest(queueItem) {
        try {
            const { url, method, headers, body } = queueItem;
            
            // Prepare request options
            const options = {
                headers: headers || {},
                timeout: 30000 // Longer timeout for sync
            };
            
            // Add body for non-GET requests
            if (body && method.toUpperCase() !== 'GET') {
                options.data = JSON.parse(body);
            }
            
            // Make the request
            const response = await apiClient.request(method, url, options);
            
            return response;
            
        } catch (error) {
            console.error('Request sync failed:', error);
            return {
                success: false,
                error: {
                    message: error.message || 'Sync failed',
                    details: error
                }
            };
        }
    }

    // Get queue size
    async getQueueSize() {
        try {
            const queue = await this.cacheService.getOfflineQueue();
            return queue.length;
        } catch (error) {
            console.error('Failed to get queue size:', error);
            return 0;
        }
    }

    // Get sync status
    getSyncStatus() {
        return {
            isSyncing: this.isSyncing,
            isOnline: navigator.onLine,
            lastSyncTime: this.lastSyncTime,
            periodicSyncActive: !!this.syncInterval
        };
    }

    // Force sync
    async forceSync() {
        if (!navigator.onLine) {
            throw new Error('Cannot sync while offline');
        }
        
        console.log('Force sync requested...');
        await this.syncPendingRequests();
    }

    // Clear sync queue
    async clearQueue() {
        try {
            const queue = await this.cacheService.getOfflineQueue();
            
            for (const item of queue) {
                await this.cacheService.removeFromOfflineQueue(item.id);
            }
            
            console.log('Sync queue cleared');
            
            this.eventBus.emit('sync:queueCleared', {
                clearedCount: queue.length,
                timestamp: Date.now()
            });
            
            return true;
            
        } catch (error) {
            console.error('Failed to clear sync queue:', error);
            return false;
        }
    }

    // Get queue items
    async getQueueItems() {
        try {
            return await this.cacheService.getOfflineQueue();
        } catch (error) {
            console.error('Failed to get queue items:', error);
            return [];
        }
    }

    // Remove specific item from queue
    async removeQueueItem(id) {
        try {
            const result = await this.cacheService.removeFromOfflineQueue(id);
            
            if (result) {
                this.eventBus.emit('sync:itemRemoved', {
                    id,
                    timestamp: Date.now()
                });
            }
            
            return result;
            
        } catch (error) {
            console.error('Failed to remove queue item:', error);
            return false;
        }
    }

    // Subscribe to sync events
    on(event, callback) {
        this.eventBus.on(event, callback);
    }

    // Unsubscribe from sync events
    off(event, callback) {
        this.eventBus.off(event, callback);
    }

    // Destroy sync service
    destroy() {
        this.stopPeriodicSync();
        
        // Remove event listeners
        window.removeEventListener('online', this.handleOnlineStatus);
        window.removeEventListener('offline', this.handleOnlineStatus);
        
        console.log('Sync service destroyed');
    }
}

export default SyncService;
