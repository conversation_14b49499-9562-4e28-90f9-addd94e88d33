// Authentication Service for Warehouse Management System
import apiClient from '../api/client.js';
import { API_CONFIG, ApiHelpers } from '../../config/api.js';
import { EventBus } from '../utils/eventBus.js';

export class AuthService {
    constructor() {
        this.currentUser = null;
        this.tokenRefreshTimer = null;
        this.eventBus = new EventBus();
        
        // Bind methods
        this.refreshToken = this.refreshToken.bind(this);
        this.handleTokenExpiry = this.handleTokenExpiry.bind(this);
    }

    // Initialize authentication service
    async init() {
        // Check if user is already authenticated
        const token = this.getStoredToken();
        if (token) {
            try {
                const isValid = await this.verifyToken(token);
                if (isValid) {
                    await this.loadCurrentUser();
                    this.scheduleTokenRefresh();
                } else {
                    this.clearAuth();
                }
            } catch (error) {
                console.error('Auth initialization failed:', error);
                this.clearAuth();
            }
        }
    }

    // Login user with credentials
    async login(username, password, remember = false) {
        try {
            const response = await apiClient.post(API_CONFIG.endpoints.auth.login, {
                username,
                password
            });

            if (response.success && response.data.token) {
                const { token, user } = response.data;
                
                // Store token
                this.storeToken(token, remember);
                
                // Set current user
                this.currentUser = user;
                
                // Schedule token refresh
                this.scheduleTokenRefresh();
                
                // Emit login event
                this.eventBus.emit('auth:login', user);
                
                console.log('User logged in successfully:', user.username);
                
                return {
                    success: true,
                    data: { user, token }
                };
            } else {
                throw new Error(response.error?.message || 'Login failed');
            }
            
        } catch (error) {
            console.error('Login failed:', error);
            return {
                success: false,
                error: {
                    message: ApiHelpers.formatError(error),
                    details: error
                }
            };
        }
    }

    // Logout user
    async logout() {
        try {
            // Clear authentication data
            this.clearAuth();
            
            // Emit logout event
            this.eventBus.emit('auth:logout');
            
            console.log('User logged out successfully');
            
            return { success: true };
            
        } catch (error) {
            console.error('Logout failed:', error);
            return {
                success: false,
                error: {
                    message: 'Logout failed',
                    details: error
                }
            };
        }
    }

    // Verify token validity
    async verifyToken(token = null) {
        try {
            const tokenToVerify = token || this.getStoredToken();
            if (!tokenToVerify) {
                return false;
            }

            const response = await apiClient.post(API_CONFIG.endpoints.auth.verify, {
                token: tokenToVerify
            });

            return response.success && response.data.valid;
            
        } catch (error) {
            console.error('Token verification failed:', error);
            return false;
        }
    }

    // Refresh authentication token
    async refreshToken() {
        try {
            const currentToken = this.getStoredToken();
            if (!currentToken) {
                throw new Error('No token to refresh');
            }

            // If refresh endpoint exists, use it; otherwise verify current token
            const endpoint = API_CONFIG.endpoints.auth.refresh || API_CONFIG.endpoints.auth.verify;
            const response = await apiClient.post(endpoint, {
                token: currentToken
            });

            if (response.success) {
                // If new token is provided, update it
                if (response.data.token) {
                    const remember = this.isTokenRemembered();
                    this.storeToken(response.data.token, remember);
                }
                
                // Update user data if provided
                if (response.data.user) {
                    this.currentUser = response.data.user;
                }
                
                // Schedule next refresh
                this.scheduleTokenRefresh();
                
                console.log('Token refreshed successfully');
                return true;
            } else {
                throw new Error('Token refresh failed');
            }
            
        } catch (error) {
            console.error('Token refresh failed:', error);
            this.handleTokenExpiry();
            return false;
        }
    }

    // Check if user is authenticated
    async isAuthenticated() {
        const token = this.getStoredToken();
        if (!token) {
            return false;
        }

        // Check token validity
        return await this.verifyToken(token);
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Load current user data
    async loadCurrentUser() {
        try {
            const token = this.getStoredToken();
            if (!token) {
                throw new Error('No authentication token');
            }

            // Verify token and get user data
            const response = await apiClient.post(API_CONFIG.endpoints.auth.verify, {
                token
            });

            if (response.success && response.data.user) {
                this.currentUser = response.data.user;
                return this.currentUser;
            } else {
                throw new Error('Failed to load user data');
            }
            
        } catch (error) {
            console.error('Failed to load current user:', error);
            this.clearAuth();
            return null;
        }
    }

    // Store authentication token
    storeToken(token, remember = false) {
        if (remember) {
            localStorage.setItem(API_CONFIG.auth.tokenKey, token);
            localStorage.setItem(`${API_CONFIG.auth.tokenKey}_remember`, 'true');
            // Remove from session storage if it exists
            sessionStorage.removeItem(API_CONFIG.auth.tokenKey);
        } else {
            sessionStorage.setItem(API_CONFIG.auth.tokenKey, token);
            // Remove from local storage if it exists
            localStorage.removeItem(API_CONFIG.auth.tokenKey);
            localStorage.removeItem(`${API_CONFIG.auth.tokenKey}_remember`);
        }
        
        // Update API client token
        apiClient.setAuthToken(token, remember);
    }

    // Get stored authentication token
    getStoredToken() {
        return localStorage.getItem(API_CONFIG.auth.tokenKey) || 
               sessionStorage.getItem(API_CONFIG.auth.tokenKey);
    }

    // Check if token is remembered (stored in localStorage)
    isTokenRemembered() {
        return localStorage.getItem(`${API_CONFIG.auth.tokenKey}_remember`) === 'true';
    }

    // Clear authentication data
    clearAuth() {
        // Clear tokens
        localStorage.removeItem(API_CONFIG.auth.tokenKey);
        localStorage.removeItem(`${API_CONFIG.auth.tokenKey}_remember`);
        sessionStorage.removeItem(API_CONFIG.auth.tokenKey);
        
        // Clear API client token
        apiClient.removeAuthToken();
        
        // Clear current user
        this.currentUser = null;
        
        // Clear token refresh timer
        if (this.tokenRefreshTimer) {
            clearTimeout(this.tokenRefreshTimer);
            this.tokenRefreshTimer = null;
        }
    }

    // Schedule token refresh
    scheduleTokenRefresh() {
        if (!API_CONFIG.auth.autoRefresh) {
            return;
        }

        // Clear existing timer
        if (this.tokenRefreshTimer) {
            clearTimeout(this.tokenRefreshTimer);
        }

        // Parse token to get expiry time
        const token = this.getStoredToken();
        if (!token) {
            return;
        }

        try {
            // Decode JWT token (simple base64 decode, not cryptographically secure)
            const payload = JSON.parse(atob(token.split('.')[1]));
            const expiryTime = payload.exp * 1000; // Convert to milliseconds
            const currentTime = Date.now();
            const timeUntilExpiry = expiryTime - currentTime;
            const refreshTime = timeUntilExpiry - API_CONFIG.auth.refreshThreshold;

            if (refreshTime > 0) {
                this.tokenRefreshTimer = setTimeout(this.refreshToken, refreshTime);
                console.log(`Token refresh scheduled in ${Math.round(refreshTime / 1000)} seconds`);
            } else {
                // Token is about to expire or already expired
                this.handleTokenExpiry();
            }
            
        } catch (error) {
            console.error('Failed to parse token for refresh scheduling:', error);
        }
    }

    // Handle token expiry
    handleTokenExpiry() {
        console.log('Token expired, logging out user');
        this.clearAuth();
        this.eventBus.emit('auth:tokenExpired');
        this.eventBus.emit('auth:logout');
    }

    // Get user permissions
    getUserPermissions() {
        if (!this.currentUser) {
            return [];
        }

        // Return permissions based on user role
        const rolePermissions = {
            admin: [
                'products:read', 'products:write', 'products:delete',
                'categories:read', 'categories:write', 'categories:delete',
                'sales:read', 'sales:write', 'sales:delete',
                'income:read', 'income:write', 'income:delete',
                'receipts:read', 'receipts:write', 'receipts:print',
                'reports:read', 'reports:export',
                'users:read', 'users:write', 'users:delete',
                'settings:read', 'settings:write'
            ],
            user: [
                'products:read', 'products:write',
                'categories:read',
                'sales:read', 'sales:write',
                'income:read', 'income:write',
                'receipts:read', 'receipts:write', 'receipts:print',
                'reports:read'
            ]
        };

        return rolePermissions[this.currentUser.role] || rolePermissions.user;
    }

    // Check if user has specific permission
    hasPermission(permission) {
        const permissions = this.getUserPermissions();
        return permissions.includes(permission);
    }

    // Check if user has any of the specified permissions
    hasAnyPermission(permissions) {
        const userPermissions = this.getUserPermissions();
        return permissions.some(permission => userPermissions.includes(permission));
    }

    // Check if user has all of the specified permissions
    hasAllPermissions(permissions) {
        const userPermissions = this.getUserPermissions();
        return permissions.every(permission => userPermissions.includes(permission));
    }

    // Subscribe to authentication events
    on(event, callback) {
        this.eventBus.on(event, callback);
    }

    // Unsubscribe from authentication events
    off(event, callback) {
        this.eventBus.off(event, callback);
    }
}

export default AuthService;
