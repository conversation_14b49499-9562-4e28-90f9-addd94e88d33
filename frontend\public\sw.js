// Service Worker for Warehouse Management PWA
const CACHE_NAME = 'warehouse-pwa-v1.0.0';
const API_CACHE_NAME = 'warehouse-api-v1.0.0';
const OFFLINE_QUEUE_NAME = 'warehouse-offline-queue';

// Static resources to cache
const STATIC_CACHE_URLS = [
    '/',
    '/index.html',
    '/manifest.json',
    '../src/css/main.css',
    '../src/js/app.js',
    '../src/js/api/client.js',
    '../src/js/services/auth.js',
    '../src/js/services/cache.js',
    '../src/js/services/sync.js',
    '../src/js/utils/helpers.js',
    'https://cdn.tailwindcss.com',
    // Icons
    'icons/icon-192x192.png',
    'icons/icon-512x512.png',
    'icons/apple-touch-icon.png'
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
    /\/api\/products/,
    /\/api\/categories/,
    /\/api\/reports/,
    /\/api\/auth\/verify/
];

// Install event - cache static resources
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .then(() => {
                console.log('Service Worker: Static files cached successfully');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Failed to cache static files', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated successfully');
                return self.clients.claim();
            })
    );
});

// Fetch event - handle network requests
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Handle API requests
    if (url.pathname.startsWith('/api/')) {
        event.respondWith(handleApiRequest(request));
        return;
    }
    
    // Handle static resources
    event.respondWith(handleStaticRequest(request));
});

// Handle API requests with cache-first or network-first strategy
async function handleApiRequest(request) {
    const url = new URL(request.url);
    const method = request.method;
    
    try {
        // For GET requests, try cache first, then network
        if (method === 'GET') {
            return await handleGetApiRequest(request);
        }
        
        // For POST/PUT/DELETE requests, try network first
        return await handleMutationApiRequest(request);
        
    } catch (error) {
        console.error('Service Worker: API request failed', error);
        
        // Return cached response if available
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline response
        return new Response(
            JSON.stringify({
                success: false,
                error: {
                    message: 'Нет соединения с сервером',
                    offline: true
                }
            }),
            {
                status: 503,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}

// Handle GET API requests (cache-first strategy)
async function handleGetApiRequest(request) {
    const cache = await caches.open(API_CACHE_NAME);
    
    // Check if this endpoint should be cached
    const shouldCache = API_CACHE_PATTERNS.some(pattern => 
        pattern.test(new URL(request.url).pathname)
    );
    
    if (shouldCache) {
        // Try cache first
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
            // Fetch fresh data in background
            fetchAndCache(request, cache);
            return cachedResponse;
        }
    }
    
    // Fetch from network
    const networkResponse = await fetch(request);
    
    // Cache successful responses
    if (shouldCache && networkResponse.ok) {
        cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
}

// Handle mutation API requests (network-first strategy)
async function handleMutationApiRequest(request) {
    try {
        // Try network first
        const networkResponse = await fetch(request);
        
        // If successful, clear related cache entries
        if (networkResponse.ok) {
            await invalidateRelatedCache(request);
        }
        
        return networkResponse;
        
    } catch (error) {
        // If network fails, queue for later sync
        await queueOfflineRequest(request);
        
        return new Response(
            JSON.stringify({
                success: true,
                data: { queued: true },
                error: null,
                _meta: { offline: true, queued: true }
            }),
            {
                status: 202,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}

// Handle static resource requests
async function handleStaticRequest(request) {
    try {
        // Try cache first
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Try network
        const networkResponse = await fetch(request);
        
        // Cache successful responses
        if (networkResponse.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        // Return cached response if available
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline page
        return new Response('Offline', { status: 503 });
    }
}

// Fetch and cache in background
async function fetchAndCache(request, cache) {
    try {
        const response = await fetch(request);
        if (response.ok) {
            cache.put(request, response.clone());
        }
    } catch (error) {
        console.log('Background fetch failed:', error);
    }
}

// Invalidate related cache entries
async function invalidateRelatedCache(request) {
    const url = new URL(request.url);
    const cache = await caches.open(API_CACHE_NAME);
    
    // Define cache invalidation rules
    const invalidationRules = {
        '/api/products': ['/api/products', '/api/reports/stock'],
        '/api/sales': ['/api/sales', '/api/reports/sales', '/api/reports/summary'],
        '/api/incomes': ['/api/incomes', '/api/reports/income', '/api/reports/summary'],
        '/api/categories': ['/api/categories', '/api/products']
    };
    
    const pathsToInvalidate = invalidationRules[url.pathname] || [];
    
    for (const path of pathsToInvalidate) {
        const keys = await cache.keys();
        for (const key of keys) {
            if (new URL(key.url).pathname.startsWith(path)) {
                await cache.delete(key);
            }
        }
    }
}

// Queue offline requests for later sync
async function queueOfflineRequest(request) {
    const db = await openOfflineDB();
    const transaction = db.transaction(['queue'], 'readwrite');
    const store = transaction.objectStore('queue');
    
    const requestData = {
        url: request.url,
        method: request.method,
        headers: Object.fromEntries(request.headers.entries()),
        body: request.method !== 'GET' ? await request.text() : null,
        timestamp: Date.now()
    };
    
    await store.add(requestData);
}

// Open IndexedDB for offline queue
function openOfflineDB() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('WarehouseOfflineDB', 1);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
        
        request.onupgradeneeded = event => {
            const db = event.target.result;
            if (!db.objectStoreNames.contains('queue')) {
                const store = db.createObjectStore('queue', { keyPath: 'id', autoIncrement: true });
                store.createIndex('timestamp', 'timestamp');
            }
        };
    });
}

// Background sync event
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        event.waitUntil(syncOfflineRequests());
    }
});

// Sync offline requests when connection is restored
async function syncOfflineRequests() {
    try {
        const db = await openOfflineDB();
        const transaction = db.transaction(['queue'], 'readwrite');
        const store = transaction.objectStore('queue');
        const requests = await store.getAll();
        
        for (const requestData of requests) {
            try {
                const response = await fetch(requestData.url, {
                    method: requestData.method,
                    headers: requestData.headers,
                    body: requestData.body
                });
                
                if (response.ok) {
                    await store.delete(requestData.id);
                    console.log('Synced offline request:', requestData.url);
                }
            } catch (error) {
                console.error('Failed to sync request:', requestData.url, error);
            }
        }
        
        // Notify clients about sync completion
        const clients = await self.clients.matchAll();
        clients.forEach(client => {
            client.postMessage({
                type: 'SYNC_COMPLETE',
                syncedCount: requests.length
            });
        });
        
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

// Push notification event
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: 'icons/icon-192x192.png',
            badge: 'icons/badge-72x72.png',
            vibrate: [100, 50, 100],
            data: data.data,
            actions: data.actions || []
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Notification click event
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    event.waitUntil(
        clients.openWindow(event.notification.data.url || '/')
    );
});

// Message event for communication with main thread
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});
