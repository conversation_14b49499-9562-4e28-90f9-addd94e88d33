// Simple Service Worker for Warehouse Management PWA
const CACHE_NAME = 'warehouse-pwa-simple-v1.0.0';

// Basic static resources to cache
const STATIC_CACHE_URLS = [
    '/',
    '/index.html',
    '/manifest.json',
    'icons/icon.svg'
];

// Install event - cache static resources
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Service Worker: Caching static files');
                // Cache files one by one to avoid failures
                return Promise.allSettled(
                    STATIC_CACHE_URLS.map(url => 
                        cache.add(url).catch(err => {
                            console.warn('Failed to cache:', url, err);
                            return null;
                        })
                    )
                );
            })
            .then(() => {
                console.log('Service Worker: Installation complete');
                return self.skipWaiting();
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated successfully');
                return self.clients.claim();
            })
    );
});

// Fetch event - simple cache-first strategy
self.addEventListener('fetch', event => {
    const { request } = event;
    
    try {
        const url = new URL(request.url);
        
        // Ignore requests from browser extensions
        if (url.protocol === 'chrome-extension:' || 
            url.protocol === 'moz-extension:' || 
            url.protocol === 'safari-extension:' ||
            url.hostname === 'invalid' ||
            url.hostname === 'localhost' && url.port !== '8081') {
            return;
        }
        
        // Only handle GET requests from our origin
        if (request.method !== 'GET' || url.origin !== location.origin) {
            return;
        }
        
        event.respondWith(handleRequest(request));
        
    } catch (error) {
        console.warn('Service Worker: Error processing request', error);
        return;
    }
});

// Simple request handler
async function handleRequest(request) {
    try {
        // Try cache first
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Try network
        const networkResponse = await fetch(request);
        
        // Cache successful responses for our static files
        if (networkResponse.ok && shouldCache(request.url)) {
            try {
                const cache = await caches.open(CACHE_NAME);
                await cache.put(request, networkResponse.clone());
            } catch (cacheError) {
                console.warn('Failed to cache response:', cacheError);
            }
        }
        
        return networkResponse;
        
    } catch (error) {
        console.warn('Request failed:', error);
        
        // Try to return cached version
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return basic offline response
        return new Response('Offline', { 
            status: 503,
            headers: { 'Content-Type': 'text/plain' }
        });
    }
}

// Check if URL should be cached
function shouldCache(url) {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    
    // Cache main files
    if (pathname === '/' || 
        pathname === '/index.html' || 
        pathname === '/manifest.json' ||
        pathname.startsWith('/icons/')) {
        return true;
    }
    
    return false;
}

// Message event for communication with main thread
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

console.log('Service Worker: Simple SW loaded');
