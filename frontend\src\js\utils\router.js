// Router for Warehouse Management PWA
export class Router {
    constructor() {
        this.routes = new Map();
        this.currentRoute = null;
        this.defaultRoute = 'dashboard';
        this.container = null;
        
        // Bind methods
        this.handlePopState = this.handlePopState.bind(this);
        this.handleHashChange = this.handleHashChange.bind(this);
    }

    // Initialize router
    init() {
        this.container = document.getElementById('main-content');
        if (!this.container) {
            throw new Error('Main content container not found');
        }

        // Register default routes
        this.registerDefaultRoutes();

        // Setup event listeners
        window.addEventListener('popstate', this.handlePopState);
        window.addEventListener('hashchange', this.handleHashChange);

        // Navigate to initial route
        const initialRoute = this.getRouteFromURL();
        this.navigate(initialRoute || this.defaultRoute, false);

        console.log('Router initialized');
    }

    // Register default routes
    registerDefaultRoutes() {
        // Dashboard
        this.register('dashboard', {
            title: 'Панель управления',
            component: () => import('../pages/dashboard.js'),
            requiresAuth: true
        });

        // Products
        this.register('products', {
            title: 'Товары',
            component: () => import('../pages/products.js'),
            requiresAuth: true
        });

        this.register('products/create', {
            title: 'Добавить товар',
            component: () => import('../pages/productForm.js'),
            requiresAuth: true
        });

        this.register('products/:id', {
            title: 'Редактировать товар',
            component: () => import('../pages/productForm.js'),
            requiresAuth: true
        });

        // Categories
        this.register('categories', {
            title: 'Категории',
            component: () => import('../pages/categories.js'),
            requiresAuth: true
        });

        // Sales
        this.register('sales', {
            title: 'Продажи',
            component: () => import('../pages/sales.js'),
            requiresAuth: true
        });

        this.register('quick-sale', {
            title: 'Быстрая продажа',
            component: () => import('../pages/quickSale.js'),
            requiresAuth: true
        });

        // Income
        this.register('income', {
            title: 'Поступления',
            component: () => import('../pages/income.js'),
            requiresAuth: true
        });

        this.register('quick-income', {
            title: 'Быстрое поступление',
            component: () => import('../pages/quickIncome.js'),
            requiresAuth: true
        });

        // Barcode scanner
        this.register('scan-barcode', {
            title: 'Сканер штрих-кодов',
            component: () => import('../pages/barcodeScanner.js'),
            requiresAuth: true
        });

        // Reports
        this.register('reports', {
            title: 'Отчеты',
            component: () => import('../pages/reports.js'),
            requiresAuth: true
        });

        this.register('reports/stock', {
            title: 'Отчет по остаткам',
            component: () => import('../pages/stockReport.js'),
            requiresAuth: true
        });

        this.register('reports/sales', {
            title: 'Отчет по продажам',
            component: () => import('../pages/salesReport.js'),
            requiresAuth: true
        });

        // Receipts
        this.register('receipts', {
            title: 'Чеки',
            component: () => import('../pages/receipts.js'),
            requiresAuth: true
        });

        // Settings
        this.register('settings', {
            title: 'Настройки',
            component: () => import('../pages/settings.js'),
            requiresAuth: true
        });

        // Profile
        this.register('profile', {
            title: 'Профиль',
            component: () => import('../pages/profile.js'),
            requiresAuth: true
        });

        // Error pages
        this.register('404', {
            title: 'Страница не найдена',
            component: () => import('../pages/notFound.js'),
            requiresAuth: false
        });

        this.register('403', {
            title: 'Доступ запрещен',
            component: () => import('../pages/forbidden.js'),
            requiresAuth: false
        });
    }

    // Register route
    register(path, config) {
        this.routes.set(path, {
            path,
            title: config.title || 'Warehouse',
            component: config.component,
            requiresAuth: config.requiresAuth !== false,
            permissions: config.permissions || [],
            beforeEnter: config.beforeEnter,
            afterEnter: config.afterEnter
        });
    }

    // Navigate to route
    async navigate(path, updateHistory = true) {
        try {
            console.log(`Navigating to: ${path}`);

            // Find matching route
            const route = this.findRoute(path);
            if (!route) {
                console.warn(`Route not found: ${path}`);
                return this.navigate('404', updateHistory);
            }

            // Check authentication
            if (route.requiresAuth && !await this.checkAuth()) {
                console.warn('Authentication required');
                return false;
            }

            // Check permissions
            if (route.permissions.length > 0 && !await this.checkPermissions(route.permissions)) {
                console.warn('Insufficient permissions');
                return this.navigate('403', updateHistory);
            }

            // Call beforeEnter hook
            if (route.beforeEnter) {
                const canEnter = await route.beforeEnter(route, this.currentRoute);
                if (!canEnter) {
                    return false;
                }
            }

            // Update URL
            if (updateHistory) {
                this.updateURL(path);
            }

            // Load and render component
            await this.renderRoute(route, path);

            // Update navigation
            this.updateNavigation(path);

            // Update page title
            document.title = `${route.title} - Система управления складом`;

            // Call afterEnter hook
            if (route.afterEnter) {
                route.afterEnter(route, this.currentRoute);
            }

            this.currentRoute = { ...route, path };
            return true;

        } catch (error) {
            console.error('Navigation failed:', error);
            this.showError('Ошибка загрузки страницы');
            return false;
        }
    }

    // Find route by path
    findRoute(path) {
        // Try exact match first
        if (this.routes.has(path)) {
            return this.routes.get(path);
        }

        // Try pattern matching
        for (const [routePath, route] of this.routes) {
            if (this.matchRoute(routePath, path)) {
                return { ...route, params: this.extractParams(routePath, path) };
            }
        }

        return null;
    }

    // Match route pattern
    matchRoute(pattern, path) {
        const patternParts = pattern.split('/');
        const pathParts = path.split('/');

        if (patternParts.length !== pathParts.length) {
            return false;
        }

        return patternParts.every((part, index) => {
            return part.startsWith(':') || part === pathParts[index];
        });
    }

    // Extract route parameters
    extractParams(pattern, path) {
        const patternParts = pattern.split('/');
        const pathParts = path.split('/');
        const params = {};

        patternParts.forEach((part, index) => {
            if (part.startsWith(':')) {
                const paramName = part.substring(1);
                params[paramName] = pathParts[index];
            }
        });

        return params;
    }

    // Render route component
    async renderRoute(route, path) {
        try {
            // Show loading state
            this.showLoading();

            // Load component
            const componentModule = await route.component();
            const Component = componentModule.default || componentModule;

            // Create component instance
            const component = new Component({
                route,
                params: route.params || {},
                router: this
            });

            // Clear container and render component
            this.container.innerHTML = '';
            await component.render(this.container);

            // Hide loading state
            this.hideLoading();

        } catch (error) {
            console.error('Failed to render route:', error);
            this.showError('Ошибка загрузки компонента');
        }
    }

    // Update URL
    updateURL(path) {
        const url = `#${path}`;
        if (window.location.hash !== url) {
            window.history.pushState({ path }, '', url);
        }
    }

    // Get route from current URL
    getRouteFromURL() {
        const hash = window.location.hash;
        return hash ? hash.substring(1) : null;
    }

    // Handle popstate event
    handlePopState(event) {
        const path = event.state?.path || this.getRouteFromURL() || this.defaultRoute;
        this.navigate(path, false);
    }

    // Handle hash change event
    handleHashChange() {
        const path = this.getRouteFromURL() || this.defaultRoute;
        this.navigate(path, false);
    }

    // Update navigation UI
    updateNavigation(currentPath) {
        // Update desktop navigation
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href.startsWith('#')) {
                const linkPath = href.substring(1);
                if (linkPath === currentPath || currentPath.startsWith(linkPath + '/')) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            }
        });

        // Update mobile navigation
        const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
        mobileNavLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href.startsWith('#')) {
                const linkPath = href.substring(1);
                if (linkPath === currentPath || currentPath.startsWith(linkPath + '/')) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            }
        });

        // Close mobile menu
        const mobileNav = document.getElementById('mobile-nav');
        if (mobileNav) {
            mobileNav.classList.add('hidden');
        }
    }

    // Check authentication
    async checkAuth() {
        // This should be implemented to check if user is authenticated
        // For now, return true
        return true;
    }

    // Check permissions
    async checkPermissions(permissions) {
        // This should be implemented to check user permissions
        // For now, return true
        return true;
    }

    // Show loading state
    showLoading() {
        this.container.innerHTML = `
            <div class="flex items-center justify-center h-64">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
                <span class="ml-3 text-gray-600">Загрузка...</span>
            </div>
        `;
    }

    // Hide loading state
    hideLoading() {
        // Loading is hidden when new content is rendered
    }

    // Show error
    showError(message) {
        this.container.innerHTML = `
            <div class="text-center py-12">
                <div class="text-red-500 text-6xl mb-4">⚠️</div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Ошибка</h2>
                <p class="text-gray-600 mb-4">${message}</p>
                <button onclick="location.reload()" class="btn btn-primary">
                    Обновить страницу
                </button>
            </div>
        `;
    }

    // Go back
    back() {
        window.history.back();
    }

    // Go forward
    forward() {
        window.history.forward();
    }

    // Get current route
    getCurrentRoute() {
        return this.currentRoute;
    }

    // Get route parameters
    getParams() {
        return this.currentRoute?.params || {};
    }

    // Get query parameters
    getQuery() {
        const params = new URLSearchParams(window.location.search);
        const query = {};
        for (const [key, value] of params) {
            query[key] = value;
        }
        return query;
    }
}

export default Router;
