// PWA Installer for Warehouse Management System
export class PWAInstaller {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.isInstallable = false;
        this.installPrompt = null;
        this.platform = this.detectPlatform();
        
        // Bind methods
        this.handleBeforeInstallPrompt = this.handleBeforeInstallPrompt.bind(this);
        this.handleAppInstalled = this.handleAppInstalled.bind(this);
        this.handleInstallClick = this.handleInstallClick.bind(this);
        this.handleDismissClick = this.handleDismissClick.bind(this);
    }

    // Initialize PWA installer
    init() {
        // Check if already installed
        this.checkInstallationStatus();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Check if install prompt should be shown
        this.checkInstallPromptConditions();
        
        console.log('PWA Installer initialized');
    }

    // Setup event listeners
    setupEventListeners() {
        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', this.handleBeforeInstallPrompt);
        
        // Listen for appinstalled event
        window.addEventListener('appinstalled', this.handleAppInstalled);
        
        // Listen for standalone mode changes
        window.addEventListener('resize', () => {
            this.checkInstallationStatus();
        });
    }

    // Handle beforeinstallprompt event
    handleBeforeInstallPrompt(event) {
        console.log('PWA install prompt available');
        
        // Prevent the mini-infobar from appearing on mobile
        event.preventDefault();
        
        // Store the event for later use
        this.deferredPrompt = event;
        this.isInstallable = true;
        
        // Show custom install prompt if conditions are met
        this.showInstallPromptIfNeeded();
    }

    // Handle app installed event
    handleAppInstalled(event) {
        console.log('PWA installed successfully');
        
        this.isInstalled = true;
        this.isInstallable = false;
        this.deferredPrompt = null;
        
        // Hide install prompt
        this.hideInstallPrompt();
        
        // Show success message
        this.showInstallSuccessMessage();
        
        // Track installation
        this.trackInstallation();
    }

    // Check installation status
    checkInstallationStatus() {
        // Check if running in standalone mode
        const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                           window.navigator.standalone ||
                           document.referrer.includes('android-app://');
        
        this.isInstalled = isStandalone;
        
        // Update UI based on installation status
        this.updateInstallUI();
    }

    // Check if install prompt should be shown
    checkInstallPromptConditions() {
        // Don't show if already installed
        if (this.isInstalled) {
            return false;
        }
        
        // Check if user has dismissed the prompt recently
        const dismissedTime = localStorage.getItem('pwa_install_dismissed');
        if (dismissedTime) {
            const daysSinceDismissed = (Date.now() - parseInt(dismissedTime)) / (1000 * 60 * 60 * 24);
            if (daysSinceDismissed < 7) { // Don't show for 7 days after dismissal
                return false;
            }
        }
        
        // Check if user has visited multiple times
        const visitCount = parseInt(localStorage.getItem('pwa_visit_count') || '0') + 1;
        localStorage.setItem('pwa_visit_count', visitCount.toString());
        
        // Show prompt after 3 visits
        return visitCount >= 3;
    }

    // Show install prompt
    showInstallPromptIfNeeded() {
        if (!this.isInstallable || this.installPrompt) {
            return;
        }
        
        if (!this.checkInstallPromptConditions()) {
            return;
        }
        
        this.showInstallPrompt();
    }

    // Show custom install prompt
    showInstallPrompt() {
        if (this.installPrompt) {
            return;
        }
        
        this.installPrompt = document.createElement('div');
        this.installPrompt.className = 'install-prompt';
        this.installPrompt.innerHTML = this.getInstallPromptHTML();
        
        // Add event listeners
        const installBtn = this.installPrompt.querySelector('.install-btn');
        const dismissBtn = this.installPrompt.querySelector('.dismiss-btn');
        
        if (installBtn) {
            installBtn.addEventListener('click', this.handleInstallClick);
        }
        
        if (dismissBtn) {
            dismissBtn.addEventListener('click', this.handleDismissClick);
        }
        
        // Add to page
        document.body.appendChild(this.installPrompt);
        
        // Animate in
        requestAnimationFrame(() => {
            this.installPrompt.classList.add('fade-in');
        });
        
        console.log('Install prompt shown');
    }

    // Get install prompt HTML
    getInstallPromptHTML() {
        const instructions = this.getInstallInstructions();
        
        return `
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                    </div>
                </div>
                <div class="flex-1 min-w-0">
                    <h3 class="text-sm font-medium text-gray-900">
                        Установить приложение
                    </h3>
                    <p class="text-sm text-gray-500 mt-1">
                        ${instructions}
                    </p>
                    <div class="mt-3 flex space-x-3">
                        <button class="install-btn btn btn-primary btn-sm">
                            Установить
                        </button>
                        <button class="dismiss-btn text-sm text-gray-500 hover:text-gray-700">
                            Не сейчас
                        </button>
                    </div>
                </div>
                <button class="dismiss-btn flex-shrink-0 text-gray-400 hover:text-gray-500">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        `;
    }

    // Get platform-specific install instructions
    getInstallInstructions() {
        switch (this.platform) {
            case 'ios':
                return 'Добавьте приложение на главный экран для быстрого доступа и работы офлайн.';
            case 'android':
                return 'Установите приложение для удобного доступа и уведомлений.';
            case 'desktop':
                return 'Установите приложение для быстрого доступа с рабочего стола.';
            default:
                return 'Установите приложение для лучшего опыта использования.';
        }
    }

    // Handle install button click
    async handleInstallClick() {
        if (!this.deferredPrompt) {
            // Show manual install instructions for iOS
            if (this.platform === 'ios') {
                this.showIOSInstallInstructions();
            }
            return;
        }
        
        try {
            // Show the install prompt
            this.deferredPrompt.prompt();
            
            // Wait for the user to respond
            const { outcome } = await this.deferredPrompt.userChoice;
            
            console.log(`Install prompt outcome: ${outcome}`);
            
            if (outcome === 'accepted') {
                console.log('User accepted the install prompt');
            } else {
                console.log('User dismissed the install prompt');
                this.trackDismissal();
            }
            
            // Clear the deferred prompt
            this.deferredPrompt = null;
            this.isInstallable = false;
            
        } catch (error) {
            console.error('Install prompt failed:', error);
        }
        
        // Hide the custom prompt
        this.hideInstallPrompt();
    }

    // Handle dismiss button click
    handleDismissClick() {
        this.hideInstallPrompt();
        this.trackDismissal();
    }

    // Hide install prompt
    hideInstallPrompt() {
        if (!this.installPrompt) {
            return;
        }
        
        // Animate out
        this.installPrompt.classList.add('fade-out');
        
        setTimeout(() => {
            if (this.installPrompt && this.installPrompt.parentNode) {
                this.installPrompt.parentNode.removeChild(this.installPrompt);
            }
            this.installPrompt = null;
        }, 300);
    }

    // Show iOS install instructions
    showIOSInstallInstructions() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-container">
                <div class="text-center">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        Установка на iOS
                    </h3>
                    <div class="space-y-3 text-sm text-gray-600 text-left">
                        <div class="flex items-center space-x-2">
                            <span class="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">1</span>
                            <span>Нажмите кнопку "Поделиться" в Safari</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">2</span>
                            <span>Выберите "На экран Домой"</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">3</span>
                            <span>Нажмите "Добавить"</span>
                        </div>
                    </div>
                    <button class="btn btn-primary mt-6" onclick="this.closest('.modal-overlay').remove()">
                        Понятно
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    // Show install success message
    showInstallSuccessMessage() {
        // This would typically show a notification
        console.log('PWA installed successfully!');
    }

    // Update install UI
    updateInstallUI() {
        // Update any UI elements based on installation status
        const installButtons = document.querySelectorAll('.pwa-install-btn');
        installButtons.forEach(btn => {
            if (this.isInstalled) {
                btn.style.display = 'none';
            } else {
                btn.style.display = this.isInstallable ? 'block' : 'none';
            }
        });
    }

    // Detect platform
    detectPlatform() {
        const userAgent = navigator.userAgent.toLowerCase();
        
        if (/iphone|ipad|ipod/.test(userAgent)) {
            return 'ios';
        } else if (/android/.test(userAgent)) {
            return 'android';
        } else {
            return 'desktop';
        }
    }

    // Track installation
    trackInstallation() {
        localStorage.setItem('pwa_installed', 'true');
        localStorage.setItem('pwa_install_date', Date.now().toString());
        
        // You could send analytics here
        console.log('PWA installation tracked');
    }

    // Track dismissal
    trackDismissal() {
        localStorage.setItem('pwa_install_dismissed', Date.now().toString());
        
        // You could send analytics here
        console.log('PWA install prompt dismissed');
    }

    // Check if PWA is installable
    isInstallableApp() {
        return this.isInstallable;
    }

    // Check if PWA is installed
    isInstalledApp() {
        return this.isInstalled;
    }

    // Get platform
    getPlatform() {
        return this.platform;
    }

    // Manually trigger install prompt
    triggerInstall() {
        if (this.isInstallable) {
            this.showInstallPrompt();
        } else if (this.platform === 'ios') {
            this.showIOSInstallInstructions();
        }
    }
}

export default PWAInstaller;
