/* Warehouse Management PWA - Main Styles */

/* CSS Variables for theming */
:root {
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-900: #1e3a8a;
    
    --success-500: #10b981;
    --warning-500: #f59e0b;
    --error-500: #ef4444;
    
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
    
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 200ms ease-in-out;
}

/* Base styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Navigation styles */
.nav-link {
    @apply text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium border-b-2 border-transparent hover:border-gray-300 transition-colors duration-200;
}

.nav-link.active {
    @apply text-primary-600 border-primary-600;
}

.mobile-nav-link {
    @apply text-gray-600 hover:bg-gray-50 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200;
}

.mobile-nav-link.active {
    @apply bg-primary-50 text-primary-700;
}

/* Button styles */
.btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
}

.btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
}

.btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

.btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-warning {
    @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-outline {
    @apply bg-white text-gray-700 border-gray-300 hover:bg-gray-50 focus:ring-primary-500;
}

.btn-sm {
    @apply px-3 py-1.5 text-xs;
}

.btn-lg {
    @apply px-6 py-3 text-base;
}

.btn:disabled {
    @apply opacity-50 cursor-not-allowed;
}

/* Form styles */
.form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
}

.form-input:invalid {
    @apply border-red-300 focus:ring-red-500 focus:border-red-500;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-error {
    @apply text-red-600 text-sm mt-1;
}

.form-group {
    @apply mb-4;
}

/* Card styles */
.card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
}

.card-body {
    @apply px-6 py-4;
}

.card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* Table styles */
.table {
    @apply min-w-full divide-y divide-gray-200;
}

.table thead {
    @apply bg-gray-50;
}

.table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table tbody tr:nth-child(even) {
    @apply bg-gray-50;
}

.table tbody tr:hover {
    @apply bg-gray-100;
}

/* Badge styles */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
    @apply bg-primary-100 text-primary-800;
}

.badge-success {
    @apply bg-green-100 text-green-800;
}

.badge-warning {
    @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
    @apply bg-red-100 text-red-800;
}

.badge-gray {
    @apply bg-gray-100 text-gray-800;
}

/* Alert styles */
.alert {
    @apply p-4 rounded-md border;
}

.alert-success {
    @apply bg-green-50 border-green-200 text-green-800;
}

.alert-warning {
    @apply bg-yellow-50 border-yellow-200 text-yellow-800;
}

.alert-error {
    @apply bg-red-50 border-red-200 text-red-800;
}

.alert-info {
    @apply bg-blue-50 border-blue-200 text-blue-800;
}

/* Loading styles */
.loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
}

.loading-dots::after {
    content: '';
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* Skeleton loading */
.skeleton {
    @apply animate-pulse bg-gray-200 rounded;
}

.skeleton-text {
    @apply h-4 bg-gray-200 rounded;
}

.skeleton-avatar {
    @apply h-10 w-10 bg-gray-200 rounded-full;
}

/* FAB (Floating Action Button) styles */
.fab-option {
    @apply text-white rounded-full p-3 shadow-lg transition-all duration-200 transform hover:scale-105 relative;
}

.fab-tooltip {
    @apply absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white text-xs rounded py-1 px-2 whitespace-nowrap opacity-0 pointer-events-none transition-opacity duration-200;
}

.fab-option:hover .fab-tooltip {
    @apply opacity-100;
}

/* Modal styles */
.modal-overlay {
    @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50;
}

.modal-container {
    @apply relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-md bg-white;
}

/* Notification styles */
.notification {
    @apply max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300;
}

.notification-enter {
    @apply translate-x-full opacity-0;
}

.notification-enter-active {
    @apply translate-x-0 opacity-100;
}

.notification-exit {
    @apply translate-x-0 opacity-100;
}

.notification-exit-active {
    @apply translate-x-full opacity-0;
}

/* Responsive utilities */
@media (max-width: 640px) {
    .mobile-hidden {
        @apply hidden;
    }
    
    .mobile-full {
        @apply w-full;
    }
    
    .mobile-stack {
        @apply flex-col space-y-2 space-x-0;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-break {
        page-break-after: always;
    }
    
    .print-avoid-break {
        page-break-inside: avoid;
    }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1f2937;
        --bg-secondary: #374151;
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
    }
}

/* Accessibility improvements */
.sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
}

/* Focus styles for better accessibility */
.focus-visible:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-100;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-400 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500;
}

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

.scale-in {
    animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* Connection status indicator */
.connection-indicator {
    @apply flex items-center space-x-2;
}

.connection-dot {
    @apply w-2 h-2 rounded-full;
}

.connection-online .connection-dot {
    @apply bg-green-500;
}

.connection-offline .connection-dot {
    @apply bg-red-500;
}

.connection-syncing .connection-dot {
    @apply bg-yellow-500 animate-pulse;
}

/* Barcode scanner styles */
.scanner-container {
    @apply relative w-full h-64 bg-black rounded-lg overflow-hidden;
}

.scanner-overlay {
    @apply absolute inset-0 flex items-center justify-center;
}

.scanner-frame {
    @apply border-2 border-white w-48 h-32 relative;
}

.scanner-corner {
    @apply absolute w-4 h-4 border-2 border-primary-500;
}

.scanner-corner.top-left {
    @apply top-0 left-0 border-r-0 border-b-0;
}

.scanner-corner.top-right {
    @apply top-0 right-0 border-l-0 border-b-0;
}

.scanner-corner.bottom-left {
    @apply bottom-0 left-0 border-r-0 border-t-0;
}

.scanner-corner.bottom-right {
    @apply bottom-0 right-0 border-l-0 border-t-0;
}

/* PWA install prompt */
.install-prompt {
    @apply fixed bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg p-4 border border-gray-200 z-40;
}

@media (min-width: 768px) {
    .install-prompt {
        @apply left-auto w-80;
    }
}
